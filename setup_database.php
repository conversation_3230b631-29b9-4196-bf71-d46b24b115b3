<?php
/**
 * WarpSync Database Setup Script
 * Script untuk membuat database dan tabel secara otomatis
 */

echo "<h1>WarpSync Database Setup</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;'>";

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';  // Sesuaikan dengan password MySQL Anda jika ada
$database_name = 'warpsync';

try {
    // Step 1: Connect to MySQL server (without database)
    echo "<h2>Step 1: Connecting to MySQL Server...</h2>";
    
    $dsn = "mysql:host=$host;charset=utf8mb4";
    $options = [
        PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES   => false,
    ];
    
    $pdo = new PDO($dsn, $username, $password, $options);
    
    echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
    echo "✓ Successfully connected to MySQL server!";
    echo "</div>";
    
    // Step 2: Create database
    echo "<h2>Step 2: Creating Database...</h2>";
    
    $sql = "CREATE DATABASE IF NOT EXISTS `$database_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
    $pdo->exec($sql);
    
    echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
    echo "✓ Database '$database_name' created successfully!";
    echo "</div>";
    
    // Step 3: Use the database
    echo "<h2>Step 3: Selecting Database...</h2>";
    
    $pdo->exec("USE `$database_name`");
    
    echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
    echo "✓ Database '$database_name' selected!";
    echo "</div>";
    
    // Step 4: Create tables
    echo "<h2>Step 4: Creating Tables...</h2>";
    
    // Create farcaster_users table
    $sql_users = "
    CREATE TABLE IF NOT EXISTS `farcaster_users` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `fid` varchar(50) NOT NULL,
        `username` varchar(100) NOT NULL,
        `display_name` varchar(200) DEFAULT NULL,
        `bio` text DEFAULT NULL,
        `followers_count` int(11) DEFAULT 0,
        `following_count` int(11) DEFAULT 0,
        `custody_address` varchar(100) DEFAULT NULL,
        `pfp_url` text DEFAULT NULL,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        `last_fetched` timestamp NOT NULL DEFAULT current_timestamp(),
        `is_active` tinyint(1) DEFAULT 1,
        PRIMARY KEY (`id`),
        UNIQUE KEY `fid` (`fid`),
        KEY `idx_fid` (`fid`),
        KEY `idx_username` (`username`),
        KEY `idx_created_at` (`created_at`),
        KEY `idx_last_fetched` (`last_fetched`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($sql_users);
    
    echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
    echo "✓ Table 'farcaster_users' created successfully!";
    echo "</div>";
    
    // Create activity_logs table
    $sql_logs = "
    CREATE TABLE IF NOT EXISTS `activity_logs` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `action_type` enum('ADD_USER','REFRESH_USER','DELETE_USER') NOT NULL,
        `fid` varchar(50) DEFAULT NULL,
        `details` text DEFAULT NULL,
        `ip_address` varchar(45) DEFAULT NULL,
        `user_agent` text DEFAULT NULL,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        PRIMARY KEY (`id`),
        KEY `idx_action_type` (`action_type`),
        KEY `idx_fid` (`fid`),
        KEY `idx_created_at` (`created_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($sql_logs);
    
    echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
    echo "✓ Table 'activity_logs' created successfully!";
    echo "</div>";
    
    // Create app_settings table
    $sql_settings = "
    CREATE TABLE IF NOT EXISTS `app_settings` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `setting_key` varchar(100) NOT NULL,
        `setting_value` text DEFAULT NULL,
        `description` text DEFAULT NULL,
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`id`),
        UNIQUE KEY `setting_key` (`setting_key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($sql_settings);
    
    echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
    echo "✓ Table 'app_settings' created successfully!";
    echo "</div>";
    
    // Step 5: Insert default settings
    echo "<h2>Step 5: Inserting Default Settings...</h2>";
    
    $default_settings = [
        ['api_rate_limit', '100', 'Maximum API calls per hour'],
        ['items_per_page', '12', 'Number of user cards per page'],
        ['cache_duration', '3600', 'Cache duration in seconds'],
        ['app_name', 'WarpSync', 'Application name'],
        ['app_version', '1.0.0', 'Application version']
    ];
    
    $sql_insert = "INSERT IGNORE INTO app_settings (setting_key, setting_value, description) VALUES (?, ?, ?)";
    $stmt = $pdo->prepare($sql_insert);
    
    foreach ($default_settings as $setting) {
        $stmt->execute($setting);
    }
    
    echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
    echo "✓ Default settings inserted successfully!";
    echo "</div>";
    
    // Step 6: Test the setup
    echo "<h2>Step 6: Testing Setup...</h2>";
    
    // Test if we can query the tables
    $test_queries = [
        "SELECT COUNT(*) as count FROM farcaster_users" => "farcaster_users",
        "SELECT COUNT(*) as count FROM activity_logs" => "activity_logs", 
        "SELECT COUNT(*) as count FROM app_settings" => "app_settings"
    ];
    
    foreach ($test_queries as $query => $table) {
        try {
            $stmt = $pdo->query($query);
            $result = $stmt->fetch();
            
            echo "<div style='color: green; padding: 5px; margin: 5px 0;'>";
            echo "✓ Table '$table': " . $result['count'] . " records";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div style='color: red; padding: 5px; margin: 5px 0;'>";
            echo "✗ Error testing table '$table': " . $e->getMessage();
            echo "</div>";
        }
    }
    
    // Success message
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2 style='margin-top: 0;'>🎉 Setup Complete!</h2>";
    echo "<p><strong>Database setup completed successfully!</strong></p>";
    echo "<p>Your WarpSync application is now ready to use.</p>";
    echo "<ul>";
    echo "<li>Database: <strong>$database_name</strong></li>";
    echo "<li>Tables created: <strong>3</strong></li>";
    echo "<li>Default settings: <strong>Configured</strong></li>";
    echo "</ul>";
    echo "</div>";
    
    // Next steps
    echo "<div style='background: #cce7ff; color: #004085; padding: 20px; border: 1px solid #99d6ff; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>Next Steps:</h3>";
    echo "<ol>";
    echo "<li><a href='test_connection.php' style='color: #004085;'>Run connection test</a> to verify everything works</li>";
    echo "<li><a href='index.php' style='color: #004085;'>Visit main page</a> to see the application</li>";
    echo "<li><a href='admin/' style='color: #004085;'>Go to admin panel</a> to manage users</li>";
    echo "<li><a href='admin/add_user.php' style='color: #004085;'>Add your first user</a> by entering a FID</li>";
    echo "</ol>";
    echo "</div>";
    
    // Show database info
    echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>Database Information:</h3>";
    echo "<table style='width: 100%; border-collapse: collapse;'>";
    echo "<tr><td style='padding: 5px; border: 1px solid #ddd;'><strong>Host:</strong></td><td style='padding: 5px; border: 1px solid #ddd;'>$host</td></tr>";
    echo "<tr><td style='padding: 5px; border: 1px solid #ddd;'><strong>Database:</strong></td><td style='padding: 5px; border: 1px solid #ddd;'>$database_name</td></tr>";
    echo "<tr><td style='padding: 5px; border: 1px solid #ddd;'><strong>Username:</strong></td><td style='padding: 5px; border: 1px solid #ddd;'>$username</td></tr>";
    echo "<tr><td style='padding: 5px; border: 1px solid #ddd;'><strong>Charset:</strong></td><td style='padding: 5px; border: 1px solid #ddd;'>utf8mb4</td></tr>";
    echo "</table>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='color: red; padding: 20px; border: 1px solid red; margin: 20px 0; border-radius: 5px;'>";
    echo "<h2>❌ Setup Failed!</h2>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<h3>Common Solutions:</h3>";
    echo "<ul>";
    echo "<li>Make sure XAMPP MySQL service is running</li>";
    echo "<li>Check if MySQL username/password is correct</li>";
    echo "<li>Verify MySQL is running on port 3306</li>";
    echo "<li>Try accessing phpMyAdmin to test MySQL connection</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; color: #856404; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>Manual Setup Instructions:</h3>";
    echo "<p>If automatic setup fails, you can create the database manually:</p>";
    echo "<ol>";
    echo "<li>Open phpMyAdmin (http://localhost/phpmyadmin)</li>";
    echo "<li>Create a new database named 'warpsync'</li>";
    echo "<li>Import the SQL file: sql/warpsync.sql</li>";
    echo "<li>Or copy and paste the SQL commands from the file</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 20px; border: 1px solid red; margin: 20px 0;'>";
    echo "<h2>❌ Unexpected Error!</h2>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</div>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    background: #f5f5f5;
    margin: 0;
    padding: 20px;
}

h1, h2, h3 {
    color: #333;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background: #f8f9fa;
}
</style>
