<?php
/**
 * WarpSync Quick Setup
 * One-click setup untuk WarpSync
 */
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WarpSync Quick Setup</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .setup-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        
        .logo {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }
        
        .step {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }
        
        .step h3 {
            margin-top: 0;
            color: #333;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .checklist {
            text-align: left;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .checklist li {
            margin: 10px 0;
            padding: 5px 0;
        }
        
        .check {
            color: #28a745;
            font-weight: bold;
        }
        
        .cross {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="logo">🚀 WarpSync</div>
        <div class="subtitle">Farcaster Profile Manager - Quick Setup</div>
        
        <?php
        // Check XAMPP status
        $mysql_running = false;
        $apache_running = false;
        
        // Check if we can connect to MySQL
        try {
            $test_conn = new PDO("mysql:host=localhost", "root", "");
            $mysql_running = true;
        } catch (Exception $e) {
            $mysql_running = false;
        }
        
        // Check if Apache is running (we're here, so probably yes)
        $apache_running = true;
        ?>
        
        <div class="checklist">
            <h3>📋 Pre-Setup Checklist</h3>
            <ul style="list-style: none; padding: 0;">
                <li><?php echo $apache_running ? '<span class="check">✓</span>' : '<span class="cross">✗</span>'; ?> Apache Web Server</li>
                <li><?php echo $mysql_running ? '<span class="check">✓</span>' : '<span class="cross">✗</span>'; ?> MySQL Database Server</li>
                <li><span class="check">✓</span> PHP (You're seeing this page!)</li>
                <li><span class="check">✓</span> WarpSync Files</li>
            </ul>
        </div>
        
        <?php if (!$mysql_running): ?>
        <div class="status error">
            <h3>❌ MySQL Not Running</h3>
            <p>Please start MySQL service in XAMPP Control Panel first!</p>
            <ol style="text-align: left;">
                <li>Open XAMPP Control Panel</li>
                <li>Click "Start" next to MySQL</li>
                <li>Wait for it to turn green</li>
                <li>Refresh this page</li>
            </ol>
        </div>
        <?php else: ?>
        
        <div class="status success">
            <h3>✅ Ready for Setup</h3>
            <p>All requirements are met. You can proceed with the setup.</p>
        </div>
        
        <div class="step">
            <h3>Step 1: Create Database</h3>
            <p>This will create the 'warpsync' database and all required tables automatically.</p>
            <a href="setup_database.php" class="btn">🗄️ Setup Database</a>
        </div>
        
        <div class="step">
            <h3>Step 2: Test Connection</h3>
            <p>Verify that everything is working correctly.</p>
            <a href="test_connection.php" class="btn btn-secondary">🔧 Test Connection</a>
        </div>
        
        <div class="step">
            <h3>Step 3: Start Using WarpSync</h3>
            <p>Access the main application and admin panel.</p>
            <a href="index.php" class="btn">🏠 Main App</a>
            <a href="admin/" class="btn">👨‍💼 Admin Panel</a>
        </div>
        
        <?php endif; ?>
        
        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee;">
            <h3>📚 Quick Start Guide</h3>
            <div style="text-align: left; background: #f8f9fa; padding: 20px; border-radius: 10px;">
                <ol>
                    <li><strong>Setup Database:</strong> Click "Setup Database" button above</li>
                    <li><strong>Test Everything:</strong> Run the connection test</li>
                    <li><strong>Add Users:</strong> Go to Admin Panel → Add User</li>
                    <li><strong>Enter FIDs:</strong> Input Farcaster IDs (e.g., 1, 2, 3)</li>
                    <li><strong>View Results:</strong> Check the main page to see user cards</li>
                </ol>
            </div>
        </div>
        
        <div style="margin-top: 30px; font-size: 0.9rem; color: #666;">
            <p>Need help? Check the README.md file or contact support.</p>
            <p>WarpSync v1.0.0 - Built with ❤️ for Farcaster community</p>
        </div>
    </div>
    
    <script>
        // Auto-refresh every 5 seconds if MySQL is not running
        <?php if (!$mysql_running): ?>
        setTimeout(function() {
            location.reload();
        }, 5000);
        <?php endif; ?>
        
        // Add some interactivity
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>
