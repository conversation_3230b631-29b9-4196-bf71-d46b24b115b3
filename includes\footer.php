<?php

/**
 * Footer Template
 * Template untuk bagian footer website
 */

if (!defined('WARPSYNC_APP')) {
    die('Direct access not permitted');
}
?>
</main>

<footer class="main-footer">
    <div class="container">
        <div class="footer-content">
            <div class="footer-logo">
                <span class="logo-icon"><i class="fas fa-sync-alt"></i></span>
                <span class="logo-text"><?php echo APP_NAME; ?></span>
            </div>

            <div class="footer-info">
                <p>&copy; <?php echo date('Y'); ?> <?php echo APP_NAME; ?>. All rights reserved.</p>
                <p>Powered by <a href="https://www.farcaster.xyz/" target="_blank">Farcaster</a></p>
            </div>

            <div class="footer-links">
                <ul>
                    <li><a href="<?php echo APP_URL; ?>">Home</a></li>
                    <li><a href="<?php echo APP_URL; ?>add_user.php">Add User</a></li>
                </ul>
            </div>
        </div>
    </div>
</footer>

<!-- Loading Overlay -->
<div id="loading-overlay" class="loading-overlay">
    <div class="spinner">
        <div class="double-bounce1"></div>
        <div class="double-bounce2"></div>
    </div>
</div>

<!-- Toast Notifications -->
<div id="toast-container" class="toast-container"></div>

<!-- JavaScript Files -->
<script src="<?php echo APP_URL; ?>assets/js/main.js"></script>
<?php if (isset($isAdmin) && $isAdmin): ?>
    <script src="<?php echo APP_URL; ?>assets/js/admin.js"></script>
<?php endif; ?>

<?php if (isset($extraScripts)): ?>
    <?php echo $extraScripts; ?>
<?php endif; ?>
</body>

</html>