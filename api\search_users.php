<?php

/**
 * API Endpoint: Search Users
 * Endpoint untuk mencari dan mengambil data users dengan pagination
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

define('WARPSYNC_APP', true);
require_once __DIR__ . '/../includes/functions.php';

/**
 * Handle GET request to search users
 */
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
        // Get parameters
        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? min(MAX_ITEMS_PER_PAGE, max(1, intval($_GET['limit']))) : ITEMS_PER_PAGE;
        $search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
        $sortBy = isset($_GET['sort']) ? sanitizeInput($_GET['sort']) : 'updated_at';
        $sortOrder = isset($_GET['order']) && strtolower($_GET['order']) === 'asc' ? 'ASC' : 'DESC';

        // Validate sort field
        $allowedSortFields = ['username', 'display_name', 'followers_count', 'following_count', 'created_at', 'updated_at', 'last_fetched'];
        if (!in_array($sortBy, $allowedSortFields)) {
            $sortBy = 'updated_at';
        }

        $offset = ($page - 1) * $limit;

        // Build query
        $whereClause = '';
        $params = [];

        if (!empty($search)) {
            $whereClause = "WHERE (username LIKE ? OR display_name LIKE ? OR bio LIKE ?)";
            $searchTerm = '%' . $search . '%';
            $params = [$searchTerm, $searchTerm, $searchTerm];
        }

        // Get total count
        $countSql = "SELECT COUNT(*) as total FROM farcaster_users " . $whereClause;
        $totalResult = fetchOne($countSql, $params);
        $total = $totalResult['total'];

        // Get users
        $sql = "SELECT * FROM farcaster_users " . $whereClause . " 
                ORDER BY " . $sortBy . " " . $sortOrder . " 
                LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;

        $users = fetchAll($sql, $params);

        // Format user data
        $formattedUsers = array_map(function ($user) {
            return [
                'id' => $user['id'],
                'fid' => $user['fid'],
                'username' => $user['username'],
                'display_name' => $user['display_name'],
                'bio' => $user['bio'],
                'followers_count' => intval($user['followers_count']),
                'following_count' => intval($user['following_count']),
                'custody_address' => $user['custody_address'],
                'verified_eth_addresses' => $user['verified_eth_addresses'],
                'verified_sol_addresses' => $user['verified_sol_addresses'],
                'primary_eth_address' => $user['primary_eth_address'],
                'primary_sol_address' => $user['primary_sol_address'],
                'pfp_url' => $user['pfp_url'],
                'created_at' => $user['created_at'],
                'updated_at' => $user['updated_at'],
                'last_fetched' => $user['last_fetched'],
                'is_active' => boolval($user['is_active']),
                'followers_formatted' => formatNumber($user['followers_count']),
                'following_formatted' => formatNumber($user['following_count']),
                'time_ago' => timeAgo($user['updated_at'])
            ];
        }, $users);

        $totalPages = ceil($total / $limit);

        echo json_encode([
            'success' => true,
            'message' => 'Users retrieved successfully',
            'data' => [
                'users' => $formattedUsers,
                'pagination' => [
                    'current_page' => $page,
                    'total_pages' => $totalPages,
                    'total_items' => $total,
                    'items_per_page' => $limit,
                    'has_next' => $page < $totalPages,
                    'has_prev' => $page > 1
                ],
                'search' => [
                    'query' => $search,
                    'sort_by' => $sortBy,
                    'sort_order' => $sortOrder
                ]
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage(),
            'data' => null
        ]);
    }
}

/**
 * Handle GET request for user statistics
 */
elseif ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['stats'])) {
    try {
        $stats = getAppStats();

        echo json_encode([
            'success' => true,
            'message' => 'Statistics retrieved successfully',
            'data' => $stats
        ]);
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage(),
            'data' => null
        ]);
    }
}

/**
 * Handle DELETE request to remove user
 */
elseif ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
    try {
        // Get JSON input
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || !isset($input['fid'])) {
            throw new Exception('FID is required');
        }

        $fid = sanitizeInput($input['fid']);

        if (!validateFID($fid)) {
            throw new Exception('Invalid FID format');
        }

        // Check if user exists
        $existingUser = getUserByFID($fid);

        if (!$existingUser) {
            throw new Exception('User not found');
        }

        // Delete user
        $result = deleteUserByFID($fid);

        if ($result > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'User deleted successfully',
                'data' => ['fid' => $fid, 'deleted' => true]
            ]);
        } else {
            throw new Exception('Failed to delete user');
        }
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage(),
            'data' => null
        ]);
    }
} else {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed',
        'data' => null
    ]);
}
