<?php
/**
 * Debug API Response
 * Cek raw response dari API untuk debug
 */

echo "<h1>🔍 Debug API Response</h1>";
echo "<style>body{font-family:Arial;max-width:1000px;margin:0 auto;padding:20px;background:#f5f5f5;}</style>";

// Test dengan FID sederhana
$test_fid = 3; // <PERSON>, pasti ada

echo "<h2>Testing FID: $test_fid</h2>";

// URL yang akan ditest
$url = "https://api.neynar.com/v2/farcaster/user/bulk?fids=" . $test_fid;

echo "<p><strong>URL:</strong> <code>$url</code></p>";

// Headers yang akan dikirim
$headers = [
    'Accept: application/json',
    'x-api-key: NEYNAR_API_DOCS',
    'User-Agent: WarpSync-Debug/1.0'
];

echo "<p><strong>Headers:</strong></p>";
echo "<ul>";
foreach ($headers as $header) {
    echo "<li><code>" . htmlspecialchars($header) . "</code></li>";
}
echo "</ul>";

// Test dengan cURL
echo "<h3>🌐 cURL Test</h3>";

$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_HTTPHEADER => $headers,
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_VERBOSE => false,
    CURLOPT_HEADER => false
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
$error = curl_error($ch);
curl_close($ch);

echo "<div style='background:#e8f4fd;padding:15px;border-radius:5px;margin:10px 0;'>";
echo "<strong>Response Info:</strong><br>";
echo "HTTP Code: <strong>$httpCode</strong><br>";
echo "Content Type: <strong>$contentType</strong><br>";
echo "cURL Error: <strong>" . ($error ?: 'None') . "</strong><br>";
echo "Response Length: <strong>" . strlen($response) . " bytes</strong>";
echo "</div>";

// Show raw response
echo "<h3>📄 Raw Response</h3>";
echo "<textarea style='width:100%;height:200px;font-family:monospace;font-size:12px;'>";
echo htmlspecialchars($response);
echo "</textarea>";

// Try to decode JSON
echo "<h3>🔍 JSON Analysis</h3>";

if ($httpCode !== 200) {
    echo "<div style='color:red;padding:10px;border:1px solid red;'>";
    echo "<strong>❌ HTTP Error $httpCode</strong><br>";
    echo "API returned non-200 status. This might be why we get HTML instead of JSON.";
    echo "</div>";
} else {
    $data = json_decode($response, true);
    $json_error = json_last_error();
    
    if ($json_error !== JSON_ERROR_NONE) {
        echo "<div style='color:red;padding:10px;border:1px solid red;'>";
        echo "<strong>❌ JSON Decode Error:</strong> " . json_last_error_msg() . "<br>";
        echo "Response is not valid JSON. First 200 chars:<br>";
        echo "<code>" . htmlspecialchars(substr($response, 0, 200)) . "...</code>";
        echo "</div>";
    } else {
        echo "<div style='color:green;padding:10px;border:1px solid green;'>";
        echo "<strong>✅ Valid JSON Response</strong>";
        echo "</div>";
        
        // Show structure
        echo "<h4>JSON Structure:</h4>";
        echo "<pre style='background:#f8f8f8;padding:10px;overflow:auto;max-height:300px;'>";
        echo htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT));
        echo "</pre>";
        
        // Extract user data
        if (isset($data['users']) && !empty($data['users'])) {
            $user = $data['users'][0];
            echo "<h4>Extracted User Data:</h4>";
            echo "<table border='1' style='border-collapse:collapse;width:100%;'>";
            echo "<tr><th>Field</th><th>Value</th></tr>";
            
            $fields = [
                'fid' => $user['fid'] ?? 'N/A',
                'username' => $user['username'] ?? 'N/A',
                'display_name' => $user['display_name'] ?? 'N/A',
                'follower_count' => $user['follower_count'] ?? 0,
                'following_count' => $user['following_count'] ?? 0,
                'custody_address' => $user['custody_address'] ?? 'N/A',
                'pfp_url' => isset($user['pfp_url']) ? 'Available' : 'N/A',
                'bio' => $user['profile']['bio']['text'] ?? 'N/A'
            ];
            
            foreach ($fields as $field => $value) {
                echo "<tr>";
                echo "<td><strong>$field</strong></td>";
                echo "<td>" . htmlspecialchars($value) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<div style='color:orange;padding:10px;border:1px solid orange;'>";
            echo "<strong>⚠️ No users found in response</strong>";
            echo "</div>";
        }
    }
}

// Test dengan file_get_contents sebagai alternatif
echo "<h3>🔄 Alternative Test (file_get_contents)</h3>";

$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => implode("\r\n", $headers),
        'timeout' => 30
    ]
]);

$response2 = @file_get_contents($url, false, $context);

if ($response2 === false) {
    echo "<div style='color:red;padding:10px;border:1px solid red;'>";
    echo "<strong>❌ file_get_contents failed</strong>";
    echo "</div>";
} else {
    echo "<div style='color:green;padding:10px;border:1px solid green;'>";
    echo "<strong>✅ file_get_contents success</strong><br>";
    echo "Length: " . strlen($response2) . " bytes<br>";
    echo "Same as cURL: " . ($response === $response2 ? 'Yes' : 'No');
    echo "</div>";
}

// Test our function
echo "<h3>🧪 Test WarpSync Function</h3>";

define('WARPSYNC_APP', true);

try {
    require_once 'config/config.php';
    require_once 'includes/functions.php';
    
    echo "<p>Testing fetchFarcasterUser($test_fid)...</p>";
    
    $userData = fetchFarcasterUser($test_fid);
    
    echo "<div style='color:green;padding:10px;border:1px solid green;'>";
    echo "<strong>✅ Function Success!</strong><br>";
    echo "Username: " . htmlspecialchars($userData['username']) . "<br>";
    echo "Display Name: " . htmlspecialchars($userData['display_name']) . "<br>";
    echo "FID: " . htmlspecialchars($userData['fid']);
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color:red;padding:10px;border:1px solid red;'>";
    echo "<strong>❌ Function Error:</strong><br>";
    echo htmlspecialchars($e->getMessage());
    echo "</div>";
}

// Recommendations
echo "<h3>💡 Troubleshooting</h3>";
echo "<div style='background:#fff3cd;padding:15px;border:1px solid #ffeaa7;border-radius:5px;'>";
echo "<strong>If you see HTML instead of JSON:</strong><br>";
echo "1. API key might be invalid or expired<br>";
echo "2. Rate limit exceeded<br>";
echo "3. API endpoint changed<br>";
echo "4. Network/firewall blocking the request<br><br>";

echo "<strong>If JSON is valid but function fails:</strong><br>";
echo "1. Check field mapping in saveUserToDatabase()<br>";
echo "2. Database connection issues<br>";
echo "3. Missing required fields<br><br>";

echo "<strong>Next steps:</strong><br>";
echo "1. If this debug shows valid JSON, the API is working<br>";
echo "2. Check your JavaScript console for frontend errors<br>";
echo "3. Check PHP error logs<br>";
echo "4. Try with different FIDs: 1, 2, 194, 191";
echo "</div>";

echo "<h3>🔗 Quick Links</h3>";
echo "<p>";
echo "<a href='test_api.php'>Full API Test</a> | ";
echo "<a href='test_connection.php'>Database Test</a> | ";
echo "<a href='admin/add_user.php'>Add User</a> | ";
echo "<a href='index.php'>Main App</a>";
echo "</p>";
?>
