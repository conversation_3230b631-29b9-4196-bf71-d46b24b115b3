<?php
/**
 * API Endpoint: Fetch User
 * Endpoint untuk mengambil data user dari Farcaster API dan menyimpan ke database
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

define('WARPSYNC_APP', true);
require_once __DIR__ . '/../includes/functions.php';

/**
 * Handle POST request to fetch user by FID
 */
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Get JSON input
        $input = json_decode(file_get_contents('php://input'), true);
        
        // Validate input
        if (!$input || !isset($input['fid'])) {
            throw new Exception('FID is required');
        }
        
        $fid = sanitizeInput($input['fid']);
        
        if (!validateFID($fid)) {
            throw new Exception('Invalid FID format. FID must be a positive number.');
        }
        
        // Check if user already exists
        $existingUser = getUserByFID($fid);
        
        if ($existingUser && isset($input['skip_if_exists']) && $input['skip_if_exists']) {
            echo json_encode([
                'success' => true,
                'message' => 'User already exists',
                'data' => [
                    'action' => 'skipped',
                    'fid' => $fid,
                    'user' => $existingUser
                ]
            ]);
            exit;
        }
        
        // Process user
        $result = processUserByFID($fid);
        
        if ($result['success']) {
            // Get updated user data
            $userData = getUserByFID($fid);
            $result['data']['user'] = $userData;
        }
        
        echo json_encode($result);
        
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage(),
            'data' => null
        ]);
    }
}

/**
 * Handle GET request to fetch user data from database
 */
elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
        if (!isset($_GET['fid'])) {
            throw new Exception('FID parameter is required');
        }
        
        $fid = sanitizeInput($_GET['fid']);
        
        if (!validateFID($fid)) {
            throw new Exception('Invalid FID format');
        }
        
        $user = getUserByFID($fid);
        
        if (!$user) {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'User not found',
                'data' => null
            ]);
            exit;
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'User found',
            'data' => $user
        ]);
        
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage(),
            'data' => null
        ]);
    }
}

/**
 * Handle batch FID processing
 */
elseif ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_GET['batch'])) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['fids']) || !is_array($input['fids'])) {
            throw new Exception('FIDs array is required');
        }
        
        $fids = $input['fids'];
        $results = [];
        $errors = [];
        
        foreach ($fids as $fid) {
            $fid = sanitizeInput($fid);
            
            if (!validateFID($fid)) {
                $errors[] = "Invalid FID: $fid";
                continue;
            }
            
            try {
                $result = processUserByFID($fid);
                $results[] = [
                    'fid' => $fid,
                    'success' => $result['success'],
                    'message' => $result['message'],
                    'action' => $result['data']['action'] ?? 'unknown'
                ];
                
                // Add small delay to avoid rate limiting
                usleep(100000); // 0.1 second
                
            } catch (Exception $e) {
                $errors[] = "FID $fid: " . $e->getMessage();
                $results[] = [
                    'fid' => $fid,
                    'success' => false,
                    'message' => $e->getMessage(),
                    'action' => 'error'
                ];
            }
        }
        
        echo json_encode([
            'success' => count($errors) === 0,
            'message' => count($errors) === 0 ? 'All users processed successfully' : 'Some users failed to process',
            'data' => [
                'results' => $results,
                'errors' => $errors,
                'total_processed' => count($results),
                'total_errors' => count($errors)
            ]
        ]);
        
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage(),
            'data' => null
        ]);
    }
}

else {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed',
        'data' => null
    ]);
}
?>
