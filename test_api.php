<?php
/**
 * Test Neynar API directly
 * Test script untuk memastikan API Neynar berfungsi
 */

echo "<h1>🧪 Neynar API Test</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;'>";

// Test FIDs
$test_fids = [1, 2, 3, 194, 191]; // Popular FIDs

echo "<h2>Testing Neynar API with sample FIDs</h2>";

foreach ($test_fids as $fid) {
    echo "<h3>Testing FID: $fid</h3>";
    
    // Neynar API endpoint
    $url = "https://api.neynar.com/v2/farcaster/user/bulk?fids=" . $fid;
    
    echo "<p><strong>URL:</strong> <a href='$url' target='_blank'>$url</a></p>";
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_HTTPHEADER => [
            'Accept: application/json',
            'x-api-key: NEYNAR_API_DOCS', // Demo API key
            'User-Agent: WarpSync/1.0'
        ],
        CURLOPT_SSL_VERIFYPEER => false
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
        echo "<strong>❌ cURL Error:</strong> $error";
        echo "</div>";
        continue;
    }
    
    if ($httpCode !== 200) {
        echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
        echo "<strong>❌ HTTP Error:</strong> $httpCode<br>";
        echo "<strong>Response:</strong> " . htmlspecialchars($response);
        echo "</div>";
        continue;
    }
    
    $data = json_decode($response, true);
    
    if (!$data) {
        echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
        echo "<strong>❌ JSON Decode Error:</strong> Invalid JSON response";
        echo "</div>";
        continue;
    }
    
    if (!isset($data['users']) || empty($data['users'])) {
        echo "<div style='color: orange; padding: 10px; border: 1px solid orange; margin: 10px 0;'>";
        echo "<strong>⚠️ No Users Found:</strong> User with FID $fid not found";
        echo "</div>";
        continue;
    }
    
    $user = $data['users'][0];
    
    echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
    echo "<strong>✅ Success!</strong> User data retrieved:<br>";
    echo "<strong>Username:</strong> " . htmlspecialchars($user['username'] ?? 'N/A') . "<br>";
    echo "<strong>Display Name:</strong> " . htmlspecialchars($user['display_name'] ?? 'N/A') . "<br>";
    echo "<strong>FID:</strong> " . htmlspecialchars($user['fid'] ?? 'N/A') . "<br>";
    echo "<strong>Followers:</strong> " . number_format($user['follower_count'] ?? 0) . "<br>";
    echo "<strong>Following:</strong> " . number_format($user['following_count'] ?? 0) . "<br>";
    echo "<strong>Custody Address:</strong> " . htmlspecialchars($user['custody_address'] ?? 'N/A') . "<br>";
    echo "<strong>PFP URL:</strong> " . (isset($user['pfp_url']) ? 'Available' : 'N/A') . "<br>";
    
    if (isset($user['profile']['bio']['text'])) {
        echo "<strong>Bio:</strong> " . htmlspecialchars(substr($user['profile']['bio']['text'], 0, 100)) . "...<br>";
    }
    echo "</div>";
    
    // Show raw JSON structure (first 500 chars)
    echo "<details style='margin: 10px 0;'>";
    echo "<summary>Raw JSON Response (first 500 chars)</summary>";
    echo "<pre style='background: #f5f5f5; padding: 10px; overflow: auto; max-height: 200px;'>";
    echo htmlspecialchars(substr(json_encode($data, JSON_PRETTY_PRINT), 0, 500)) . "...";
    echo "</pre>";
    echo "</details>";
    
    echo "<hr>";
}

// Test our function
echo "<h2>Testing WarpSync Function</h2>";

define('WARPSYNC_APP', true);

try {
    require_once 'config/config.php';
    require_once 'includes/functions.php';
    
    echo "<p>Testing fetchFarcasterUser() function with FID 3...</p>";
    
    $userData = fetchFarcasterUser(3);
    
    echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
    echo "<strong>✅ Function Test Success!</strong><br>";
    echo "<strong>Username:</strong> " . htmlspecialchars($userData['username'] ?? 'N/A') . "<br>";
    echo "<strong>Display Name:</strong> " . htmlspecialchars($userData['display_name'] ?? 'N/A') . "<br>";
    echo "<strong>FID:</strong> " . htmlspecialchars($userData['fid'] ?? 'N/A') . "<br>";
    echo "</div>";
    
    // Test database save
    echo "<p>Testing saveUserToDatabase() function...</p>";
    
    $result = saveUserToDatabase($userData);
    
    echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
    echo "<strong>✅ Database Save Success!</strong><br>";
    echo "<strong>Action:</strong> " . htmlspecialchars($result['action']) . "<br>";
    echo "<strong>FID:</strong> " . htmlspecialchars($result['fid']) . "<br>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
    echo "<strong>❌ Function Test Error:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

echo "<h2>📋 Summary</h2>";
echo "<div style='background: #f0f8ff; padding: 15px; border: 1px solid #0066cc; border-radius: 5px;'>";
echo "<p><strong>API Endpoint:</strong> https://api.neynar.com/v2/farcaster/user/bulk</p>";
echo "<p><strong>Required Header:</strong> x-api-key: NEYNAR_API_DOCS</p>";
echo "<p><strong>Parameter:</strong> fids (comma-separated FIDs)</p>";
echo "<p><strong>Response Format:</strong> JSON with 'users' array</p>";
echo "</div>";

echo "<h2>🚀 Next Steps</h2>";
echo "<div style='background: #f0f0f0; padding: 15px; margin: 10px 0;'>";
echo "<ol>";
echo "<li>If API tests pass, your WarpSync should work now</li>";
echo "<li>Try adding a user: <a href='admin/add_user.php'>admin/add_user.php</a></li>";
echo "<li>Check main page: <a href='index.php'>index.php</a></li>";
echo "<li>For production, get your own Neynar API key from <a href='https://neynar.com' target='_blank'>neynar.com</a></li>";
echo "</ol>";
echo "</div>";

echo "</div>";
?>

<style>
body {
    background: #f5f5f5;
    margin: 0;
    padding: 20px;
}

h1, h2, h3 {
    color: #333;
}

a {
    color: #0066cc;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

details {
    cursor: pointer;
}

summary {
    font-weight: bold;
    padding: 5px;
    background: #e9ecef;
    border-radius: 3px;
}

pre {
    font-size: 12px;
    line-height: 1.4;
}
</style>
