/**
 * WarpSync Admin JavaScript
 * Additional JavaScript functionality for admin pages
 */

class WarpSyncAdmin {
    constructor() {
        this.apiBase = window.location.origin + window.location.pathname.replace(/\/admin\/[^\/]*$/, '') + '/api/';
        this.init();
    }
    
    init() {
        this.bindAdminEvents();
        this.initializeAdminFeatures();
    }
    
    bindAdminEvents() {
        // Auto-refresh stats every 30 seconds on dashboard
        if (window.location.pathname.includes('/admin/index.php') || window.location.pathname.endsWith('/admin/')) {
            setInterval(() => {
                this.refreshStats();
            }, 30000);
        }
        
        // Form enhancements
        this.enhanceForms();
        
        // Keyboard shortcuts
        this.bindKeyboardShortcuts();
    }
    
    initializeAdminFeatures() {
        // Initialize tooltips
        this.initTooltips();
        
        // Initialize confirmation dialogs
        this.initConfirmations();
        
        // Auto-save form data
        this.initAutoSave();
    }
    
    // Refresh dashboard stats
    async refreshStats() {
        try {
            const response = await fetch(this.apiBase + 'search_users.php?stats=1');
            const data = await response.json();
            
            if (data.success) {
                this.updateStatsDisplay(data.data);
            }
        } catch (error) {
            console.error('Failed to refresh stats:', error);
        }
    }
    
    updateStatsDisplay(stats) {
        // Update total users
        const totalUsersElement = document.querySelector('.stat-card .stat-number');
        if (totalUsersElement && stats.total_users !== undefined) {
            totalUsersElement.textContent = this.formatNumber(stats.total_users);
        }
        
        // Update users today
        const usersTodayElements = document.querySelectorAll('.stat-card .stat-number');
        if (usersTodayElements[1] && stats.users_today !== undefined) {
            usersTodayElements[1].textContent = this.formatNumber(stats.users_today);
        }
        
        // Update last update time
        const lastUpdateElements = document.querySelectorAll('.stat-card .stat-number');
        if (lastUpdateElements[2] && stats.last_update) {
            lastUpdateElements[2].textContent = this.timeAgo(stats.last_update);
        }
    }
    
    // Form enhancements
    enhanceForms() {
        // Add loading states to form submissions
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn && !submitBtn.disabled) {
                    const originalText = submitBtn.innerHTML;
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
                    
                    // Re-enable after 10 seconds as fallback
                    setTimeout(() => {
                        if (submitBtn.disabled) {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = originalText;
                        }
                    }, 10000);
                }
            });
        });
        
        // Auto-resize textareas
        const textareas = document.querySelectorAll('textarea');
        textareas.forEach(textarea => {
            textarea.addEventListener('input', () => {
                textarea.style.height = 'auto';
                textarea.style.height = textarea.scrollHeight + 'px';
            });
        });
        
        // FID validation
        const fidInputs = document.querySelectorAll('input[name="fid"], input[type="number"]');
        fidInputs.forEach(input => {
            input.addEventListener('input', (e) => {
                const value = e.target.value;
                if (value && (!Number.isInteger(Number(value)) || Number(value) <= 0)) {
                    e.target.setCustomValidity('FID must be a positive integer');
                } else {
                    e.target.setCustomValidity('');
                }
            });
        });
    }
    
    // Keyboard shortcuts
    bindKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K for search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.querySelector('#search-input, #admin-search-input');
                if (searchInput) {
                    searchInput.focus();
                }
            }
            
            // Ctrl/Cmd + N for new user
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                window.location.href = 'add_user.php';
            }
            
            // Escape to close modals/overlays
            if (e.key === 'Escape') {
                const overlay = document.querySelector('.loading-overlay.active');
                if (overlay) {
                    overlay.classList.remove('active');
                }
                
                const mobileMenu = document.querySelector('.mobile-menu.active');
                if (mobileMenu) {
                    mobileMenu.classList.remove('active');
                }
            }
        });
    }
    
    // Initialize tooltips
    initTooltips() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                this.showTooltip(e.target, e.target.dataset.tooltip);
            });
            
            element.addEventListener('mouseleave', () => {
                this.hideTooltip();
            });
        });
    }
    
    showTooltip(element, text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = text;
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
        
        setTimeout(() => tooltip.classList.add('show'), 10);
    }
    
    hideTooltip() {
        const tooltip = document.querySelector('.tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    }
    
    // Initialize confirmation dialogs
    initConfirmations() {
        const confirmButtons = document.querySelectorAll('[data-confirm]');
        confirmButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const message = button.dataset.confirm;
                if (!confirm(message)) {
                    e.preventDefault();
                    e.stopPropagation();
                }
            });
        });
    }
    
    // Auto-save form data
    initAutoSave() {
        const forms = document.querySelectorAll('form[data-autosave]');
        forms.forEach(form => {
            const formId = form.dataset.autosave;
            
            // Load saved data
            this.loadFormData(form, formId);
            
            // Save on input
            form.addEventListener('input', () => {
                this.saveFormData(form, formId);
            });
            
            // Clear on submit
            form.addEventListener('submit', () => {
                this.clearFormData(formId);
            });
        });
    }
    
    saveFormData(form, formId) {
        const formData = new FormData(form);
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        localStorage.setItem('warpsync_form_' + formId, JSON.stringify(data));
    }
    
    loadFormData(form, formId) {
        const savedData = localStorage.getItem('warpsync_form_' + formId);
        if (savedData) {
            try {
                const data = JSON.parse(savedData);
                
                for (let [key, value] of Object.entries(data)) {
                    const input = form.querySelector(`[name="${key}"]`);
                    if (input && input.type !== 'hidden') {
                        input.value = value;
                    }
                }
            } catch (error) {
                console.error('Failed to load form data:', error);
            }
        }
    }
    
    clearFormData(formId) {
        localStorage.removeItem('warpsync_form_' + formId);
    }
    
    // Utility methods
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }
    
    timeAgo(dateString) {
        const now = new Date();
        const date = new Date(dateString);
        const diffInSeconds = Math.floor((now - date) / 1000);
        
        if (diffInSeconds < 60) return 'just now';
        if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + ' minutes ago';
        if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + ' hours ago';
        if (diffInSeconds < 2592000) return Math.floor(diffInSeconds / 86400) + ' days ago';
        if (diffInSeconds < 31536000) return Math.floor(diffInSeconds / 2592000) + ' months ago';
        
        return Math.floor(diffInSeconds / 31536000) + ' years ago';
    }
    
    // Batch operations
    async batchRefreshUsers(fids) {
        const results = [];
        const errors = [];
        
        for (const fid of fids) {
            try {
                const response = await fetch(this.apiBase + 'refresh_user.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ fid: fid })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    results.push(`FID ${fid}: Success`);
                } else {
                    errors.push(`FID ${fid}: ${data.message}`);
                }
                
                // Small delay to avoid rate limiting
                await new Promise(resolve => setTimeout(resolve, 100));
                
            } catch (error) {
                errors.push(`FID ${fid}: ${error.message}`);
            }
        }
        
        return { results, errors };
    }
    
    // Export data
    exportUsersData(format = 'json') {
        // This would implement data export functionality
        console.log('Export functionality would be implemented here');
    }
}

// Initialize admin features when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.warpSyncAdmin = new WarpSyncAdmin();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WarpSyncAdmin;
}
