<?php

/**
 * WarpSync - Main Page
 * Halaman utama untuk menampilkan semua profil Farcaster users
 */

define('WARPSYNC_APP', true);
require_once 'config/config.php';
require_once 'includes/functions.php';

// Page variables
$pageTitle = 'Farcaster Profile Manager';
$pageHeader = 'WarpSync';
$pageDescription = 'Manage and display Farcaster user profiles with ease';
$currentPage = 'home';
?>

<?php include 'includes/header.php'; ?>

<div class="container">
    <!-- Search and Controls -->
    <div class="search-controls">
        <div class="search-form">
            <input type="text" id="search-input" class="search-input" placeholder="Search users by username, display name, or bio...">
            <button id="search-btn" class="btn btn-primary">
                <i class="fas fa-search"></i> Search
            </button>
            <button id="refresh-all-btn" class="btn btn-secondary">
                <i class="fas fa-sync-alt"></i> Refresh All
            </button>
            <a href="add_user.php" class="btn btn-success">
                <i class="fas fa-plus"></i> Add User
            </a>
        </div>
    </div>

    <!-- Users Grid -->
    <div id="users-container" class="users-grid">
        <!-- Users will be loaded here via JavaScript -->
        <div class="empty-state">
            <i class="fas fa-spinner fa-spin"></i>
            <h3>Loading users...</h3>
            <p>Please wait while we fetch the user data.</p>
        </div>
    </div>

    <!-- Pagination -->
    <div id="pagination-container"></div>
</div>

<?php include 'includes/footer.php'; ?>