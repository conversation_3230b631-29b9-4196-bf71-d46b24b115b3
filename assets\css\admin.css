/* WarpSync Admin CSS */

/* Admin-specific styles */

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.stat-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    opacity: 0;
    transition: var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-lg);
    color: var(--text-dark);
    font-size: 1.5rem;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    font-family: var(--font-heading);
    line-height: 1;
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    color: var(--text-muted);
    font-size: 0.9rem;
    font-weight: 500;
}

/* Admin Actions */
.admin-actions {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.admin-actions h2 {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

/* Recent Activity */
.recent-activity {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
}

.recent-activity h2 {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.activity-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition-normal);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-item:hover {
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: var(--bg-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-md);
    color: var(--primary-color);
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.activity-details {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-xs);
}

.activity-time {
    color: var(--text-muted);
    font-size: 0.8rem;
}

/* Form Styles */
.form-container {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.add-user-form {
    max-width: 600px;
    margin: 0 auto;
}

.form-section {
    margin-bottom: var(--spacing-xl);
}

.form-section h3 {
    font-family: var(--font-heading);
    font-size: 1.25rem;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.form-input,
.form-textarea {
    width: 100%;
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 1rem;
    transition: var(--transition-normal);
    font-family: var(--font-primary);
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.form-help {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-top: var(--spacing-sm);
}

.form-divider {
    text-align: center;
    margin: var(--spacing-xl) 0;
    position: relative;
}

.form-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-color);
}

.form-divider span {
    background: var(--bg-card);
    padding: 0 var(--spacing-md);
    color: var(--text-muted);
    font-weight: 500;
}

.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    margin-top: var(--spacing-xl);
}

/* Alert Messages */
.alert {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-lg);
    border: 1px solid;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    border-color: var(--success-color);
    color: var(--success-color);
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    border-color: var(--warning-color);
    color: var(--warning-color);
}

.alert-error {
    background: rgba(239, 68, 68, 0.1);
    border-color: var(--error-color);
    color: var(--error-color);
}

/* Instructions */
.instructions {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
}

.instructions h3 {
    font-family: var(--font-heading);
    font-size: 1.25rem;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.instruction-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.instruction-item {
    display: flex;
    align-items: flex-start;
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    transition: var(--transition-normal);
}

.instruction-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.instruction-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-md);
    color: var(--text-dark);
    font-size: 1.25rem;
    flex-shrink: 0;
}

.instruction-content h4 {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.instruction-content p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .instruction-grid {
        grid-template-columns: 1fr;
    }
    
    .instruction-item {
        flex-direction: column;
        text-align: center;
    }
    
    .instruction-icon {
        margin-right: 0;
        margin-bottom: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .form-container,
    .admin-actions,
    .recent-activity,
    .instructions {
        padding: var(--spacing-md);
    }
    
    .stat-card {
        flex-direction: column;
        text-align: center;
    }
    
    .stat-icon {
        margin-right: 0;
        margin-bottom: var(--spacing-md);
    }
}

/* Management Controls */
.management-controls {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    align-items: center;
    justify-content: space-between;
}

.control-group {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

/* Management Table */
.management-table-container {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    overflow: hidden;
    margin-bottom: var(--spacing-xl);
}

.management-table {
    width: 100%;
}

.table-header {
    display: grid;
    grid-template-columns: 80px 1fr 150px 120px 120px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    color: var(--text-primary);
}

.table-row {
    display: grid;
    grid-template-columns: 80px 1fr 150px 120px 120px;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition-normal);
}

.table-row:hover {
    background: var(--bg-secondary);
}

.table-row:last-child {
    border-bottom: none;
}

.table-cell {
    padding: var(--spacing-md);
    display: flex;
    align-items: center;
    min-height: 60px;
}

.table-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 2px solid var(--border-color);
}

.user-info-table {
    flex: 1;
}

.user-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.user-username {
    color: var(--primary-color);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-xs);
}

.user-fid {
    color: var(--text-muted);
    font-size: 0.8rem;
}

.user-stats-table {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.user-stats-table div {
    margin-bottom: var(--spacing-xs);
}

.update-time {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.table-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.loading-state {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-muted);
}

.loading-state i {
    font-size: 2rem;
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
}

/* Toast for management page */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-xl);
    transform: translateX(100%);
    transition: var(--transition-normal);
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.error {
    border-left: 4px solid var(--error-color);
}

.toast-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.toast-title {
    font-weight: 600;
    color: var(--text-primary);
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: 1.2rem;
}

.toast-message {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Responsive Management Table */
@media (max-width: 768px) {
    .management-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .control-group {
        justify-content: center;
    }

    .table-header,
    .table-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .table-cell {
        padding: var(--spacing-sm);
        min-height: auto;
        border-bottom: 1px solid var(--border-color);
    }

    .table-cell:last-child {
        border-bottom: none;
    }

    .table-header .table-cell {
        display: none;
    }

    .table-row .table-cell:before {
        content: attr(data-label);
        font-weight: 600;
        color: var(--text-muted);
        display: block;
        margin-bottom: var(--spacing-xs);
    }
}
