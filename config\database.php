<?php
/**
 * Database Configuration for WarpSync
 * Konfigurasi koneksi database MySQL
 */

class Database {
    // Database credentials
    private $host = 'localhost';
    private $db_name = 'warpsync';
    private $username = 'root';  // Sesuaikan dengan username MySQL Anda
    private $password = '';      // Sesuaikan dengan password MySQL Anda
    private $charset = 'utf8mb4';
    
    public $conn;
    
    /**
     * Membuat koneksi database menggunakan PDO
     */
    public function getConnection() {
        $this->conn = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            
            $options = [
                PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES   => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ];
            
            $this->conn = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch(PDOException $exception) {
            error_log("Connection error: " . $exception->getMessage());
            throw new Exception("Database connection failed: " . $exception->getMessage());
        }
        
        return $this->conn;
    }
    
    /**
     * Test koneksi database
     */
    public function testConnection() {
        try {
            $conn = $this->getConnection();
            if ($conn) {
                return ['status' => 'success', 'message' => 'Database connection successful'];
            }
        } catch (Exception $e) {
            return ['status' => 'error', 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Tutup koneksi database
     */
    public function closeConnection() {
        $this->conn = null;
    }
}

/**
 * Function helper untuk mendapatkan koneksi database
 */
function getDBConnection() {
    $database = new Database();
    return $database->getConnection();
}

/**
 * Function untuk execute query dengan error handling
 */
function executeQuery($sql, $params = []) {
    try {
        $conn = getDBConnection();
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch (PDOException $e) {
        error_log("Query error: " . $e->getMessage());
        throw new Exception("Database query failed: " . $e->getMessage());
    }
}

/**
 * Function untuk fetch single row
 */
function fetchOne($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt->fetch();
}

/**
 * Function untuk fetch multiple rows
 */
function fetchAll($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt->fetchAll();
}

/**
 * Function untuk insert data dan return last insert ID
 */
function insertData($sql, $params = []) {
    $conn = getDBConnection();
    $stmt = $conn->prepare($sql);
    $stmt->execute($params);
    return $conn->lastInsertId();
}

/**
 * Function untuk update/delete data dan return affected rows
 */
function updateData($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt->rowCount();
}
?>
