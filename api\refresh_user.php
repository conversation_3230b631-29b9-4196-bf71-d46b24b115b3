<?php

/**
 * API Endpoint: Refresh User
 * Endpoint untuk memperbarui data user dari Farcaster API
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

define('WARPSYNC_APP', true);
require_once __DIR__ . '/../includes/functions.php';

/**
 * Handle refresh all users
 */
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_GET['all'])) {
    try {
        // Get all users
        $users = fetchAll("SELECT fid FROM farcaster_users ORDER BY last_fetched ASC");

        $results = [];
        $errors = [];
        $totalProcessed = 0;

        foreach ($users as $user) {
            $fid = $user['fid'];

            try {
                $result = refreshUserData($fid);
                $results[] = [
                    'fid' => $fid,
                    'success' => $result['success'],
                    'message' => $result['message'],
                    'action' => $result['data']['action'] ?? 'unknown'
                ];

                $totalProcessed++;

                // Add small delay to avoid rate limiting
                usleep(100000); // 0.1 second

            } catch (Exception $e) {
                $errors[] = "FID $fid: " . $e->getMessage();
                $results[] = [
                    'fid' => $fid,
                    'success' => false,
                    'message' => $e->getMessage(),
                    'action' => 'error'
                ];
            }
        }

        echo json_encode([
            'success' => count($errors) === 0,
            'message' => count($errors) === 0 ? 'All users refreshed successfully' : 'Some users failed to refresh',
            'data' => [
                'results' => $results,
                'errors' => $errors,
                'total_processed' => $totalProcessed,
                'total_errors' => count($errors)
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage(),
            'data' => null
        ]);
    }
}

/**
 * Handle POST request to refresh user data
 */
elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Get JSON input
        $input = json_decode(file_get_contents('php://input'), true);

        // Validate input
        if (!$input || !isset($input['fid'])) {
            throw new Exception('FID is required');
        }

        $fid = sanitizeInput($input['fid']);

        if (!validateFID($fid)) {
            throw new Exception('Invalid FID format. FID must be a positive number.');
        }

        // Check if user exists
        $existingUser = getUserByFID($fid);

        if (!$existingUser) {
            throw new Exception('User not found in database');
        }

        // Refresh user data
        $result = refreshUserData($fid);

        if ($result['success']) {
            // Get updated user data
            $userData = getUserByFID($fid);
            $result['data']['user'] = $userData;
        }

        echo json_encode($result);
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage(),
            'data' => null
        ]);
    }
} else {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed',
        'data' => null
    ]);
}
