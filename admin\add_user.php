<?php
/**
 * WarpSync Add User Page
 * Halaman untuk menambahkan user baru berdasarkan FID
 */

define('WARPSYNC_APP', true);
require_once '../config/config.php';
require_once '../includes/functions.php';

// Page variables
$pageTitle = 'Add User';
$pageHeader = 'Add New User';
$pageDescription = 'Add Farcaster users by entering their FID (Farcaster ID)';
$currentPage = 'add_user';
$isAdmin = true;

// Handle form submission
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Verify CSRF token
        if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
            throw new Exception('Invalid CSRF token');
        }
        
        $fids = [];
        
        // Handle single FID
        if (!empty($_POST['fid'])) {
            $fid = sanitizeInput($_POST['fid']);
            if (validateFID($fid)) {
                $fids[] = $fid;
            } else {
                throw new Exception('Invalid FID format: ' . $fid);
            }
        }
        
        // Handle multiple FIDs
        if (!empty($_POST['fids_bulk'])) {
            $bulkFids = explode("\n", $_POST['fids_bulk']);
            foreach ($bulkFids as $fid) {
                $fid = trim(sanitizeInput($fid));
                if (!empty($fid)) {
                    if (validateFID($fid)) {
                        $fids[] = $fid;
                    } else {
                        throw new Exception('Invalid FID format: ' . $fid);
                    }
                }
            }
        }
        
        if (empty($fids)) {
            throw new Exception('Please enter at least one FID');
        }
        
        // Remove duplicates
        $fids = array_unique($fids);
        
        $results = [];
        $errors = [];
        
        foreach ($fids as $fid) {
            try {
                $result = processUserByFID($fid);
                if ($result['success']) {
                    $results[] = "FID {$fid}: " . $result['message'];
                } else {
                    $errors[] = "FID {$fid}: " . $result['message'];
                }
            } catch (Exception $e) {
                $errors[] = "FID {$fid}: " . $e->getMessage();
            }
        }
        
        if (empty($errors)) {
            $message = 'All users processed successfully:<br>' . implode('<br>', $results);
            $messageType = 'success';
        } else {
            $message = 'Some users failed to process:<br>' . implode('<br>', array_merge($results, $errors));
            $messageType = 'warning';
        }
        
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $messageType = 'error';
    }
}

$csrfToken = generateCSRFToken();
?>

<?php include '../includes/header.php'; ?>

<div class="container">
    <?php if ($message): ?>
        <div class="alert alert-<?php echo $messageType; ?>">
            <?php echo $message; ?>
        </div>
    <?php endif; ?>
    
    <!-- Add User Form -->
    <div class="form-container">
        <form method="POST" class="add-user-form">
            <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
            
            <!-- Single FID Input -->
            <div class="form-section">
                <h3>Add Single User</h3>
                <div class="form-group">
                    <label for="fid">Farcaster ID (FID)</label>
                    <input type="number" id="fid" name="fid" class="form-input" 
                           placeholder="Enter FID (e.g., 1, 2, 3)" min="1">
                    <div class="form-help">
                        Enter a single Farcaster ID. You can find FIDs on Warpcast profiles or use Farcaster tools.
                    </div>
                </div>
            </div>
            
            <div class="form-divider">
                <span>OR</span>
            </div>
            
            <!-- Bulk FID Input -->
            <div class="form-section">
                <h3>Add Multiple Users</h3>
                <div class="form-group">
                    <label for="fids_bulk">Multiple FIDs (one per line)</label>
                    <textarea id="fids_bulk" name="fids_bulk" class="form-textarea" rows="8" 
                              placeholder="Enter multiple FIDs, one per line:&#10;1&#10;2&#10;3&#10;4"></textarea>
                    <div class="form-help">
                        Enter multiple FIDs, one per line. Duplicates will be automatically removed.
                    </div>
                </div>
            </div>
            
            <!-- Submit Button -->
            <div class="form-actions">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus"></i> Add User(s)
                </button>
                <a href="index.php" class="btn btn-secondary btn-lg">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
            </div>
        </form>
    </div>
    
    <!-- Instructions -->
    <div class="instructions">
        <h3>How to Find FIDs</h3>
        <div class="instruction-grid">
            <div class="instruction-item">
                <div class="instruction-icon">
                    <i class="fas fa-user"></i>
                </div>
                <div class="instruction-content">
                    <h4>Warpcast Profile</h4>
                    <p>Visit a user's Warpcast profile. The FID is usually visible in the URL or profile information.</p>
                </div>
            </div>
            
            <div class="instruction-item">
                <div class="instruction-icon">
                    <i class="fas fa-search"></i>
                </div>
                <div class="instruction-content">
                    <h4>Farcaster Tools</h4>
                    <p>Use Farcaster directory tools or APIs to search for users and get their FIDs.</p>
                </div>
            </div>
            
            <div class="instruction-item">
                <div class="instruction-icon">
                    <i class="fas fa-code"></i>
                </div>
                <div class="instruction-content">
                    <h4>API Lookup</h4>
                    <p>Use Farcaster API endpoints to look up users by username and get their FID.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const singleFidInput = document.getElementById('fid');
    const bulkFidsInput = document.getElementById('fids_bulk');
    
    // Clear other input when one is used
    singleFidInput.addEventListener('input', function() {
        if (this.value) {
            bulkFidsInput.value = '';
        }
    });
    
    bulkFidsInput.addEventListener('input', function() {
        if (this.value) {
            singleFidInput.value = '';
        }
    });
    
    // Form validation
    const form = document.querySelector('.add-user-form');
    form.addEventListener('submit', function(e) {
        const singleFid = singleFidInput.value.trim();
        const bulkFids = bulkFidsInput.value.trim();
        
        if (!singleFid && !bulkFids) {
            e.preventDefault();
            alert('Please enter at least one FID');
            return;
        }
        
        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
    });
});
</script>

<?php include '../includes/footer.php'; ?>
