# WarpSync - Farcaster Profile Manager

WarpSync adalah aplikasi web untuk mengelola dan menampilkan profil pengguna Farcaster berdasarkan FID (Farcaster ID).

## Fitur Utama
- Input FID untuk mengambil data profil dari Warpcast API
- Tampilan card responsive dan modern
- Detail view dengan expand functionality
- Refresh data per user atau global
- Halaman admin untuk management
- Search dan pagination

## Teknologi
- Frontend: HTML5, CSS3, JavaScript
- Backend: PHP
- Database: MySQL
- API: Farcaster/Warpcast API

## Struktur Folder
```
warpsync/
├── index.php                 # Halaman utama
├── admin/                    # Halaman admin
│   ├── index.php            # Dashboard admin
│   ├── add_user.php         # Form input FID
│   └── manage.php           # Kelola users
├── api/                     # Backend API
│   ├── fetch_user.php       # Ambil data dari Farcaster
│   ├── refresh_user.php     # Refresh data user
│   └── search_users.php     # Search functionality
├── config/                  # Konfigurasi
│   ├── database.php         # Koneksi database
│   └── config.php           # Konfigurasi umum
├── assets/                  # Static files
│   ├── css/
│   │   ├── style.css        # Main stylesheet
│   │   └── admin.css        # Admin stylesheet
│   ├── js/
│   │   ├── main.js          # Main JavaScript
│   │   └── admin.js         # Admin JavaScript
│   └── images/              # Images
├── includes/                # PHP includes
│   ├── header.php           # Header template
│   ├── footer.php           # Footer template
│   └── functions.php        # Helper functions
└── sql/                     # Database schema
    └── warpsync.sql         # Database structure
```

## Instalasi
1. Clone atau download project ke folder htdocs
2. Import database dari `sql/warpsync.sql`
3. Konfigurasi database di `config/database.php`
4. Akses aplikasi melalui browser

## Penggunaan
- Halaman utama: Tampilan semua profil user
- Admin panel: `/admin/` untuk mengelola data
- API endpoints: `/api/` untuk operasi backend
