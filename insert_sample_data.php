<?php

/**
 * Insert Sample Data
 * Insert sample Farcaster users untuk testing tanpa API
 */

echo "<h1>📊 Insert Sample Data</h1>";
echo "<style>body{font-family:Arial;max-width:800px;margin:0 auto;padding:20px;background:#f5f5f5;}</style>";

// Check if WARPSYNC_APP is already defined
if (!defined('WARPSYNC_APP')) {
    define('WARPSYNC_APP', true);
}

try {
    require_once 'config/config.php';
    require_once 'includes/functions.php';

    echo "<p>Inserting sample Farcaster users for testing...</p>";

    // Sample data berdasarkan user Farcaster yang populer
    $sampleUsers = [
        [
            'fid' => '1',
            'username' => 'farcaster',
            'display_name' => 'Farcaster',
            'bio' => 'A protocol for decentralized social apps',
            'followers_count' => 50000,
            'following_count' => 100,
            'custody_address' => '0x8773442740c17c9d0f0b87022c722f9a136206ed',
            'pfp_url' => 'https://imagedelivery.net/BXluQx4ige9GuW0Ia56BHw/99cb5165-1a7c-4e1b-8c87-5c4e35b6b500/rectcrop3'
        ],
        [
            'fid' => '2',
            'username' => 'v',
            'display_name' => 'Varun Srinivasan',
            'bio' => 'Working on Farcaster and Warpcast',
            'followers_count' => 45000,
            'following_count' => 500,
            'custody_address' => '0x4114e33eb831858649ea3702e1c9a2db3f626446',
            'pfp_url' => 'https://imagedelivery.net/BXluQx4ige9GuW0Ia56BHw/4c21d014-f0d5-4c36-aa1c-8d0a8e2b4500/rectcrop3'
        ],
        [
            'fid' => '3',
            'username' => 'dwr',
            'display_name' => 'Dan Romero',
            'bio' => 'Co-founder of Farcaster. Former VP at Coinbase.',
            'followers_count' => 60000,
            'following_count' => 800,
            'custody_address' => '0x6b0bda3f2ffed5efc83fa8c024acff1dd45793f1',
            'pfp_url' => 'https://imagedelivery.net/BXluQx4ige9GuW0Ia56BHw/7c21d014-f0d5-4c36-aa1c-8d0a8e2b4500/rectcrop3'
        ],
        [
            'fid' => '194',
            'username' => 'rish',
            'display_name' => 'Rish',
            'bio' => 'Building at Neynar. Farcaster infrastructure.',
            'followers_count' => 15000,
            'following_count' => 1200,
            'custody_address' => '0x1234567890123456789012345678901234567890',
            'pfp_url' => 'https://imagedelivery.net/BXluQx4ige9GuW0Ia56BHw/8c21d014-f0d5-4c36-aa1c-8d0a8e2b4500/rectcrop3'
        ],
        [
            'fid' => '191',
            'username' => 'balajis',
            'display_name' => 'Balaji',
            'bio' => 'Entrepreneur, investor, and technologist.',
            'followers_count' => 80000,
            'following_count' => 300,
            'custody_address' => '0x9876543210987654321098765432109876543210',
            'pfp_url' => 'https://imagedelivery.net/BXluQx4ige9GuW0Ia56BHw/9c21d014-f0d5-4c36-aa1c-8d0a8e2b4500/rectcrop3'
        ]
    ];

    $inserted = 0;
    $updated = 0;
    $errors = 0;

    foreach ($sampleUsers as $user) {
        try {
            // Check if user exists
            $existing = fetchOne("SELECT id FROM farcaster_users WHERE fid = ?", [$user['fid']]);

            if ($existing) {
                // Update existing
                $sql = "UPDATE farcaster_users SET 
                        username = ?, display_name = ?, bio = ?, 
                        followers_count = ?, following_count = ?, 
                        custody_address = ?, pfp_url = ?, 
                        updated_at = NOW(), last_fetched = NOW()
                        WHERE fid = ?";

                $params = [
                    $user['username'],
                    $user['display_name'],
                    $user['bio'],
                    $user['followers_count'],
                    $user['following_count'],
                    $user['custody_address'],
                    $user['pfp_url'],
                    $user['fid']
                ];

                updateData($sql, $params);
                $updated++;

                echo "<div style='color:orange;padding:5px;margin:5px 0;border:1px solid orange;'>";
                echo "🔄 Updated: {$user['username']} (FID: {$user['fid']})";
                echo "</div>";
            } else {
                // Insert new
                $sql = "INSERT INTO farcaster_users 
                        (fid, username, display_name, bio, followers_count, following_count, 
                         custody_address, pfp_url, created_at, updated_at, last_fetched) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), NOW())";

                $params = [
                    $user['fid'],
                    $user['username'],
                    $user['display_name'],
                    $user['bio'],
                    $user['followers_count'],
                    $user['following_count'],
                    $user['custody_address'],
                    $user['pfp_url']
                ];

                insertData($sql, $params);
                $inserted++;

                echo "<div style='color:green;padding:5px;margin:5px 0;border:1px solid green;'>";
                echo "✅ Inserted: {$user['username']} (FID: {$user['fid']})";
                echo "</div>";
            }

            // Log activity
            logActivity('ADD_USER', $user['fid'], 'Sample data inserted');
        } catch (Exception $e) {
            $errors++;
            echo "<div style='color:red;padding:5px;margin:5px 0;border:1px solid red;'>";
            echo "❌ Error with {$user['username']}: " . $e->getMessage();
            echo "</div>";
        }
    }

    echo "<div style='background:#d4edda;color:#155724;padding:20px;border:1px solid #c3e6cb;border-radius:5px;margin:20px 0;'>";
    echo "<h2>📊 Summary</h2>";
    echo "<strong>Inserted:</strong> $inserted users<br>";
    echo "<strong>Updated:</strong> $updated users<br>";
    echo "<strong>Errors:</strong> $errors users<br>";
    echo "<strong>Total processed:</strong> " . count($sampleUsers) . " users";
    echo "</div>";

    // Show current data
    echo "<h2>📋 Current Users in Database</h2>";

    $users = fetchAll("SELECT * FROM farcaster_users ORDER BY fid ASC");

    if (empty($users)) {
        echo "<p>No users found in database.</p>";
    } else {
        echo "<table border='1' style='border-collapse:collapse;width:100%;'>";
        echo "<tr style='background:#f8f9fa;'>";
        echo "<th>FID</th><th>Username</th><th>Display Name</th><th>Followers</th><th>Following</th><th>Last Updated</th>";
        echo "</tr>";

        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>{$user['fid']}</td>";
            echo "<td>@{$user['username']}</td>";
            echo "<td>" . htmlspecialchars($user['display_name']) . "</td>";
            echo "<td>" . number_format($user['followers_count']) . "</td>";
            echo "<td>" . number_format($user['following_count']) . "</td>";
            echo "<td>" . timeAgo($user['updated_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    echo "<div style='background:#cce7ff;color:#004085;padding:15px;border:1px solid #99d6ff;border-radius:5px;margin:20px 0;'>";
    echo "<h3>🚀 Next Steps</h3>";
    echo "<p>Sample data has been inserted! Now you can:</p>";
    echo "<ol>";
    echo "<li><a href='index.php'>View main page</a> - Should show user cards</li>";
    echo "<li><a href='admin/'>Go to admin panel</a> - Manage users</li>";
    echo "<li><a href='debug_api.php'>Debug API</a> - Test real API calls</li>";
    echo "<li><a href='admin/add_user.php'>Add more users</a> - Try with real API</li>";
    echo "</ol>";
    echo "</div>";
} catch (Exception $e) {
    echo "<div style='color:red;padding:20px;border:1px solid red;margin:20px 0;'>";
    echo "<h2>❌ Error</h2>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Make sure database is set up properly. Run <a href='setup.php'>setup.php</a> first.</p>";
    echo "</div>";
}
