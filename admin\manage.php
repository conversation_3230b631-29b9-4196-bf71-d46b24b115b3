<?php
/**
 * WarpSync Manage Users Page
 * Halaman untuk mengelola users yang sudah ada
 */

define('WARPSYNC_APP', true);
require_once '../config/config.php';
require_once '../includes/functions.php';

// Page variables
$pageTitle = 'Manage Users';
$pageHeader = 'Manage Users';
$pageDescription = 'View, edit, and manage existing Farcaster users';
$currentPage = 'manage';
$isAdmin = true;
?>

<?php include '../includes/header.php'; ?>

<div class="container">
    <!-- Management Controls -->
    <div class="management-controls">
        <div class="control-group">
            <input type="text" id="admin-search-input" class="search-input" placeholder="Search users...">
            <button id="admin-search-btn" class="btn btn-primary">
                <i class="fas fa-search"></i> Search
            </button>
        </div>
        
        <div class="control-group">
            <select id="sort-select" class="form-input">
                <option value="updated_at">Sort by Last Updated</option>
                <option value="username">Sort by Username</option>
                <option value="display_name">Sort by Display Name</option>
                <option value="followers_count">Sort by Followers</option>
                <option value="created_at">Sort by Date Added</option>
            </select>
            
            <select id="order-select" class="form-input">
                <option value="desc">Descending</option>
                <option value="asc">Ascending</option>
            </select>
        </div>
        
        <div class="control-group">
            <button id="refresh-all-manage" class="btn btn-warning">
                <i class="fas fa-sync-alt"></i> Refresh All
            </button>
            <a href="add_user.php" class="btn btn-success">
                <i class="fas fa-plus"></i> Add User
            </a>
        </div>
    </div>
    
    <!-- Users Management Table -->
    <div class="management-table-container">
        <div id="users-table-container">
            <!-- Table will be loaded here via JavaScript -->
            <div class="loading-state">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Loading users...</p>
            </div>
        </div>
    </div>
    
    <!-- Pagination -->
    <div id="manage-pagination-container"></div>
</div>

<script>
class UserManagement {
    constructor() {
        this.apiBase = '../api/';
        this.currentPage = 1;
        this.currentSearch = '';
        this.currentSort = 'updated_at';
        this.currentOrder = 'desc';
        this.isLoading = false;
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadUsers();
    }
    
    bindEvents() {
        // Search
        const searchInput = document.getElementById('admin-search-input');
        const searchBtn = document.getElementById('admin-search-btn');
        
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.searchUsers(e.target.value);
                }, 500);
            });
        }
        
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.searchUsers(searchInput.value);
            });
        }
        
        // Sort controls
        const sortSelect = document.getElementById('sort-select');
        const orderSelect = document.getElementById('order-select');
        
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.currentSort = e.target.value;
                this.loadUsers();
            });
        }
        
        if (orderSelect) {
            orderSelect.addEventListener('change', (e) => {
                this.currentOrder = e.target.value;
                this.loadUsers();
            });
        }
        
        // Refresh all
        const refreshAllBtn = document.getElementById('refresh-all-manage');
        if (refreshAllBtn) {
            refreshAllBtn.addEventListener('click', () => {
                this.refreshAllUsers();
            });
        }
    }
    
    async loadUsers(page = 1, search = '') {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading();
        
        try {
            const params = new URLSearchParams({
                page: page,
                search: search || this.currentSearch,
                sort: this.currentSort,
                order: this.currentOrder,
                limit: 20
            });
            
            const response = await fetch(`${this.apiBase}search_users.php?${params}`);
            const data = await response.json();
            
            if (data.success) {
                this.renderUsersTable(data.data.users);
                this.renderPagination(data.data.pagination);
                this.currentPage = page;
                this.currentSearch = search || this.currentSearch;
            } else {
                this.showError('Error loading users: ' + data.message);
            }
        } catch (error) {
            this.showError('Failed to load users: ' + error.message);
        } finally {
            this.isLoading = false;
            this.hideLoading();
        }
    }
    
    searchUsers(query) {
        this.currentSearch = query;
        this.loadUsers(1, query);
    }
    
    async refreshUser(fid) {
        try {
            const response = await fetch(`${this.apiBase}refresh_user.php`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ fid: fid })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess('User refreshed successfully');
                this.loadUsers(this.currentPage, this.currentSearch);
            } else {
                this.showError('Failed to refresh user: ' + data.message);
            }
        } catch (error) {
            this.showError('Error refreshing user: ' + error.message);
        }
    }
    
    async deleteUser(fid) {
        if (!confirm('Are you sure you want to delete this user?')) {
            return;
        }
        
        try {
            const response = await fetch(`${this.apiBase}search_users.php`, {
                method: 'DELETE',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ fid: fid })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess('User deleted successfully');
                this.loadUsers(this.currentPage, this.currentSearch);
            } else {
                this.showError('Failed to delete user: ' + data.message);
            }
        } catch (error) {
            this.showError('Error deleting user: ' + error.message);
        }
    }
    
    async refreshAllUsers() {
        if (!confirm('This will refresh all users data. This may take a while. Continue?')) {
            return;
        }
        
        try {
            this.showLoading();
            
            const response = await fetch(`${this.apiBase}refresh_user.php?all=1`, {
                method: 'POST'
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess('All users refreshed successfully');
                this.loadUsers(this.currentPage, this.currentSearch);
            } else {
                this.showError('Some users failed to refresh: ' + data.message);
            }
        } catch (error) {
            this.showError('Error refreshing users: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }
    
    renderUsersTable(users) {
        const container = document.getElementById('users-table-container');
        if (!container) return;
        
        if (users.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-users"></i>
                    <h3>No users found</h3>
                    <p>Try adjusting your search criteria or add some users.</p>
                </div>
            `;
            return;
        }
        
        let tableHTML = `
            <div class="management-table">
                <div class="table-header">
                    <div class="table-cell">Avatar</div>
                    <div class="table-cell">User Info</div>
                    <div class="table-cell">Stats</div>
                    <div class="table-cell">Last Updated</div>
                    <div class="table-cell">Actions</div>
                </div>
        `;
        
        users.forEach(user => {
            const avatarUrl = user.pfp_url || `https://via.placeholder.com/50x50/334155/cbd5e1?text=${user.username.charAt(0).toUpperCase()}`;
            
            tableHTML += `
                <div class="table-row">
                    <div class="table-cell">
                        <img src="${avatarUrl}" alt="${user.display_name}" class="table-avatar"
                             onerror="this.src='https://via.placeholder.com/50x50/334155/cbd5e1?text=${user.username.charAt(0).toUpperCase()}'">
                    </div>
                    <div class="table-cell">
                        <div class="user-info-table">
                            <div class="user-name">${user.display_name || user.username}</div>
                            <div class="user-username">@${user.username}</div>
                            <div class="user-fid">FID: ${user.fid}</div>
                        </div>
                    </div>
                    <div class="table-cell">
                        <div class="user-stats-table">
                            <div>${user.followers_formatted} followers</div>
                            <div>${user.following_formatted} following</div>
                        </div>
                    </div>
                    <div class="table-cell">
                        <div class="update-time">${user.time_ago}</div>
                    </div>
                    <div class="table-cell">
                        <div class="table-actions">
                            <button class="btn btn-sm btn-secondary" onclick="userManagement.refreshUser('${user.fid}')">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <button class="btn btn-sm btn-error" onclick="userManagement.deleteUser('${user.fid}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });
        
        tableHTML += '</div>';
        container.innerHTML = tableHTML;
    }
    
    renderPagination(pagination) {
        // Similar to main.js pagination rendering
        const container = document.getElementById('manage-pagination-container');
        if (!container || pagination.total_pages <= 1) {
            if (container) container.innerHTML = '';
            return;
        }
        
        let paginationHTML = '<div class="pagination">';
        
        // Previous button
        if (pagination.has_prev) {
            paginationHTML += `<button class="pagination-btn" onclick="userManagement.loadUsers(${pagination.current_page - 1})">
                <i class="fas fa-chevron-left"></i>
            </button>`;
        }
        
        // Page numbers (simplified)
        for (let i = Math.max(1, pagination.current_page - 2); i <= Math.min(pagination.total_pages, pagination.current_page + 2); i++) {
            const activeClass = i === pagination.current_page ? 'active' : '';
            paginationHTML += `<button class="pagination-btn ${activeClass}" onclick="userManagement.loadUsers(${i})">${i}</button>`;
        }
        
        // Next button
        if (pagination.has_next) {
            paginationHTML += `<button class="pagination-btn" onclick="userManagement.loadUsers(${pagination.current_page + 1})">
                <i class="fas fa-chevron-right"></i>
            </button>`;
        }
        
        paginationHTML += '</div>';
        container.innerHTML = paginationHTML;
    }
    
    showLoading() {
        const container = document.getElementById('users-table-container');
        if (container) {
            container.innerHTML = `
                <div class="loading-state">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading users...</p>
                </div>
            `;
        }
    }
    
    hideLoading() {
        // Loading will be replaced by table content
    }
    
    showSuccess(message) {
        this.showToast(message, 'success');
    }
    
    showError(message) {
        this.showToast(message, 'error');
    }
    
    showToast(message, type) {
        // Simple toast implementation
        const toast = document.createElement('div');
        toast.className = `toast ${type} show`;
        toast.innerHTML = `
            <div class="toast-header">
                <div class="toast-title">${type === 'success' ? 'Success' : 'Error'}</div>
                <button class="toast-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="toast-message">${message}</div>
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 5000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.userManagement = new UserManagement();
});
</script>

<?php include '../includes/footer.php'; ?>
