<?php

/**
 * WarpSync Configuration File
 * Konfigurasi umum aplikasi
 */

// Prevent direct access
if (!defined('WARPSYNC_APP')) {
    define('WARPSYNC_APP', true);
}

// Error reporting (set to 0 for production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('Asia/Jakarta');

// Application settings
define('APP_NAME', 'WarpSync');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/farcaster/');

// API Configuration - Using Neynar API (the correct Farcaster API)
define('FARCASTER_API_BASE', 'https://api.neynar.com/v2/farcaster/');
define('NEYNAR_API_KEY', 'NEYNAR_API_DOCS'); // Default demo key, replace with your own
define('API_RATE_LIMIT', 100); // requests per hour
define('API_TIMEOUT', 30); // seconds

// Pagination settings
define('ITEMS_PER_PAGE', 12);
define('MAX_ITEMS_PER_PAGE', 50);

// Cache settings
define('CACHE_DURATION', 3600); // 1 hour in seconds
define('CACHE_DIR', __DIR__ . '/../cache/');

// Upload settings (for future features)
define('UPLOAD_DIR', __DIR__ . '/../uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// Security settings
define('SESSION_TIMEOUT', 3600); // 1 hour
define('CSRF_TOKEN_LENGTH', 32);

// Database settings (imported from database.php)
require_once __DIR__ . '/database.php';

/**
 * Application helper functions
 */

/**
 * Sanitize input data
 */
function sanitizeInput($data)
{
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate FID format
 */
function validateFID($fid)
{
    return is_numeric($fid) && $fid > 0;
}

/**
 * Generate CSRF token
 */
function generateCSRFToken()
{
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }

    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(CSRF_TOKEN_LENGTH));
    }

    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token)
{
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }

    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Log activity
 */
function logActivity($action_type, $fid = null, $details = null)
{
    try {
        $sql = "INSERT INTO activity_logs (action_type, fid, details, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?)";

        $params = [
            $action_type,
            $fid,
            $details,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ];

        insertData($sql, $params);
    } catch (Exception $e) {
        error_log("Failed to log activity: " . $e->getMessage());
    }
}

/**
 * Format number with K/M suffix
 */
function formatNumber($number)
{
    if ($number >= 1000000) {
        return round($number / 1000000, 1) . 'M';
    } elseif ($number >= 1000) {
        return round($number / 1000, 1) . 'K';
    }
    return number_format($number);
}

/**
 * Time ago function
 */
function timeAgo($datetime)
{
    $time = time() - strtotime($datetime);

    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time / 60) . ' minutes ago';
    if ($time < 86400) return floor($time / 3600) . ' hours ago';
    if ($time < 2592000) return floor($time / 86400) . ' days ago';
    if ($time < 31536000) return floor($time / 2592000) . ' months ago';

    return floor($time / 31536000) . ' years ago';
}

/**
 * Create cache directory if not exists
 */
if (!file_exists(CACHE_DIR)) {
    mkdir(CACHE_DIR, 0755, true);
}

/**
 * Create upload directory if not exists
 */
if (!file_exists(UPLOAD_DIR)) {
    mkdir(UPLOAD_DIR, 0755, true);
}

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
