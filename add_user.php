<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

$currentPage = 'add_user';
$pageTitle = 'Add User - ' . APP_NAME;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $fid = trim($_POST['fid'] ?? '');
    
    $errors = [];
    $success = '';
    
    // Validation
    if (empty($username)) {
        $errors[] = 'Username is required';
    }
    
    if (empty($fid) || !is_numeric($fid)) {
        $errors[] = 'Valid FID is required';
    }
    
    if (empty($errors)) {
        try {
            // Check if user already exists
            $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ? OR fid = ?");
            $stmt->execute([$username, $fid]);
            
            if ($stmt->fetch()) {
                $errors[] = 'User with this username or FID already exists';
            } else {
                // Insert new user
                $stmt = $pdo->prepare("
                    INSERT INTO users (username, fid, created_at) 
                    VALUES (?, ?, NOW())
                ");
                
                if ($stmt->execute([$username, $fid])) {
                    $success = 'User added successfully! Data will be fetched automatically.';
                    
                    // Clear form
                    $username = '';
                    $fid = '';
                } else {
                    $errors[] = 'Failed to add user';
                }
            }
        } catch (PDOException $e) {
            $errors[] = 'Database error: ' . $e->getMessage();
        }
    }
}

include 'includes/header.php';
?>

<div class="container">
    <div class="page-header">
        <h1><i class="fas fa-user-plus"></i> Add New User</h1>
        <p>Add a Farcaster user to track their profile and activity</p>
    </div>

    <?php if (!empty($errors)): ?>
        <div class="alert alert-error">
            <i class="fas fa-exclamation-triangle"></i>
            <div>
                <?php foreach ($errors as $error): ?>
                    <div><?php echo htmlspecialchars($error); ?></div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <div><?php echo htmlspecialchars($success); ?></div>
        </div>
    <?php endif; ?>

    <div class="form-container">
        <form method="POST" class="add-user-form">
            <div class="form-group">
                <label for="username">
                    <i class="fas fa-user"></i>
                    Username
                </label>
                <input 
                    type="text" 
                    id="username" 
                    name="username" 
                    value="<?php echo htmlspecialchars($username ?? ''); ?>"
                    placeholder="Enter Farcaster username (without @)"
                    required
                >
                <small class="form-help">Enter the username without the @ symbol</small>
            </div>

            <div class="form-group">
                <label for="fid">
                    <i class="fas fa-hashtag"></i>
                    FID (Farcaster ID)
                </label>
                <input 
                    type="number" 
                    id="fid" 
                    name="fid" 
                    value="<?php echo htmlspecialchars($fid ?? ''); ?>"
                    placeholder="Enter numeric FID"
                    required
                >
                <small class="form-help">The numeric Farcaster ID of the user</small>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    Add User
                </button>
                <a href="<?php echo APP_URL; ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    Back to Home
                </a>
            </div>
        </form>
    </div>

    <div class="info-card">
        <h3><i class="fas fa-info-circle"></i> How to find FID</h3>
        <p>You can find a user's FID by:</p>
        <ul>
            <li>Visiting their Farcaster profile on Warpcast</li>
            <li>Using Farcaster explorer tools</li>
            <li>Checking the URL when viewing their profile</li>
        </ul>
    </div>
</div>

<style>
.form-container {
    max-width: 600px;
    margin: 0 auto;
    background: var(--card-bg);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: var(--shadow-lg);
}

.add-user-form .form-group {
    margin-bottom: 1.5rem;
}

.add-user-form label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.add-user-form input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.add-user-form input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-help {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.info-card {
    max-width: 600px;
    margin: 2rem auto 0;
    background: var(--card-bg);
    border-radius: 16px;
    padding: 1.5rem;
    border-left: 4px solid var(--primary-color);
}

.info-card h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.info-card ul {
    margin: 0.5rem 0 0 1rem;
}

.info-card li {
    margin-bottom: 0.25rem;
    color: var(--text-secondary);
}

@media (max-width: 768px) {
    .form-container,
    .info-card {
        margin-left: 1rem;
        margin-right: 1rem;
        padding: 1.5rem;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
