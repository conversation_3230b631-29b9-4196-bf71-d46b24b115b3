<?php
/**
 * WarpSync CLI Test Script
 * Run this via command line: php test.php
 */

echo "\n";
echo "🧪 WarpSync Connection Test\n";
echo "===========================\n\n";

define('WARPSYNC_APP', true);

try {
    require_once 'config/config.php';
    
    echo "📡 Testing database connection...\n";
    
    $database = new Database();
    $result = $database->testConnection();
    
    if ($result['status'] === 'success') {
        echo "✅ Database connection: SUCCESS\n";
        
        $conn = $database->getConnection();
        
        // Test tables
        echo "\n📋 Checking tables...\n";
        
        $tables = ['farcaster_users', 'activity_logs', 'app_settings'];
        
        foreach ($tables as $table) {
            try {
                $stmt = $conn->query("SELECT COUNT(*) as count FROM $table");
                $result = $stmt->fetch();
                echo "  ✓ $table: {$result['count']} records\n";
            } catch (Exception $e) {
                echo "  ✗ $table: ERROR - " . $e->getMessage() . "\n";
            }
        }
        
        // Test API endpoints
        echo "\n🌐 Testing API endpoints...\n";
        
        $base_url = 'http://localhost' . dirname($_SERVER['SCRIPT_NAME']);
        $endpoints = [
            'search_users.php' => 'Search Users',
            'fetch_user.php' => 'Fetch User',
            'refresh_user.php' => 'Refresh User'
        ];
        
        foreach ($endpoints as $endpoint => $name) {
            $url = $base_url . '/api/' . $endpoint;
            echo "  📍 $name: $url\n";
        }
        
        // Test configuration
        echo "\n⚙️  Configuration check...\n";
        echo "  ✓ App Name: " . APP_NAME . "\n";
        echo "  ✓ App Version: " . APP_VERSION . "\n";
        echo "  ✓ Items per page: " . ITEMS_PER_PAGE . "\n";
        echo "  ✓ API Rate limit: " . API_RATE_LIMIT . "\n";
        
        // Test file permissions
        echo "\n📁 File permissions...\n";
        $dirs = ['config/', 'api/', 'assets/', 'admin/'];
        
        foreach ($dirs as $dir) {
            if (is_dir($dir) && is_readable($dir)) {
                echo "  ✓ $dir: Readable\n";
            } else {
                echo "  ✗ $dir: Not readable or missing\n";
            }
        }
        
        echo "\n🎉 All tests passed!\n";
        echo "===================\n";
        echo "Your WarpSync installation is ready to use.\n\n";
        
        echo "🚀 Quick start:\n";
        echo "1. Open browser: http://localhost/farcaster/\n";
        echo "2. Admin panel: http://localhost/farcaster/admin/\n";
        echo "3. Add user: http://localhost/farcaster/admin/add_user.php\n\n";
        
    } else {
        echo "❌ Database connection: FAILED\n";
        echo "Error: " . $result['message'] . "\n\n";
        
        echo "💡 Try running setup first:\n";
        echo "php setup.php\n\n";
    }
    
} catch (Exception $e) {
    echo "❌ Test failed!\n";
    echo "Error: " . $e->getMessage() . "\n\n";
    
    echo "💡 Solutions:\n";
    echo "1. Run setup: php setup.php\n";
    echo "2. Check XAMPP MySQL is running\n";
    echo "3. Check config/database.php settings\n\n";
}

echo "WarpSync CLI Test v1.0.0\n";
echo "========================\n";
?>
