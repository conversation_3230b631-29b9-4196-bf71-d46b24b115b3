<?php
/**
 * Update Database for Multiple Addresses
 * Add columns untuk handle multiple verified addresses
 */

echo "<h1>🔧 Update Database for Multiple Addresses</h1>";
echo "<style>body{font-family:Arial;max-width:800px;margin:0 auto;padding:20px;background:#f5f5f5;}</style>";

if (!defined('WARPSYNC_APP')) {
    define('WARPSYNC_APP', true);
}

try {
    require_once 'config/config.php';
    
    echo "<p>Adding new columns for multiple verified addresses...</p>";
    
    $conn = getDBConnection();
    
    // Check if columns already exist
    $stmt = $conn->query("DESCRIBE farcaster_users");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $newColumns = [
        'verified_eth_addresses' => 'JSON',
        'verified_sol_addresses' => 'JSON', 
        'primary_eth_address' => 'VARCHAR(100)',
        'primary_sol_address' => 'VARCHAR(100)'
    ];
    
    foreach ($newColumns as $columnName => $columnType) {
        if (!in_array($columnName, $columns)) {
            echo "<div style='color:blue;padding:5px;margin:5px 0;border:1px solid blue;'>";
            echo "➕ Adding column: $columnName ($columnType)";
            echo "</div>";
            
            $sql = "ALTER TABLE farcaster_users ADD COLUMN $columnName $columnType";
            $conn->exec($sql);
            
            echo "<div style='color:green;padding:5px;margin:5px 0;border:1px solid green;'>";
            echo "✅ Column $columnName added successfully";
            echo "</div>";
        } else {
            echo "<div style='color:orange;padding:5px;margin:5px 0;border:1px solid orange;'>";
            echo "⚠️ Column $columnName already exists";
            echo "</div>";
        }
    }
    
    echo "<div style='background:#d4edda;color:#155724;padding:20px;border:1px solid #c3e6cb;border-radius:5px;margin:20px 0;'>";
    echo "<h2>✅ Database Updated Successfully!</h2>";
    echo "<p>New columns added to handle multiple verified addresses:</p>";
    echo "<ul>";
    echo "<li><strong>verified_eth_addresses</strong> - JSON array of all ETH addresses</li>";
    echo "<li><strong>verified_sol_addresses</strong> - JSON array of all SOL addresses</li>";
    echo "<li><strong>primary_eth_address</strong> - Primary ETH address</li>";
    echo "<li><strong>primary_sol_address</strong> - Primary SOL address</li>";
    echo "</ul>";
    echo "</div>";
    
    // Show current table structure
    echo "<h2>📋 Updated Table Structure</h2>";
    
    $stmt = $conn->query("DESCRIBE farcaster_users");
    $tableStructure = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse:collapse;width:100%;'>";
    echo "<tr style='background:#f8f9fa;'>";
    echo "<th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th>";
    echo "</tr>";
    
    foreach ($tableStructure as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<div style='background:#cce7ff;color:#004085;padding:15px;border:1px solid #99d6ff;border-radius:5px;margin:20px 0;'>";
    echo "<h3>🚀 Next Steps</h3>";
    echo "<ol>";
    echo "<li>Database structure updated successfully</li>";
    echo "<li>Functions will be updated to handle multiple addresses</li>";
    echo "<li>Frontend will show all verified addresses</li>";
    echo "<li>Try refreshing existing users to get new address data</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color:red;padding:20px;border:1px solid red;margin:20px 0;'>";
    echo "<h2>❌ Error</h2>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>
