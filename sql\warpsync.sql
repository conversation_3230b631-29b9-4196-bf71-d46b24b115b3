-- WarpSync Database Schema
-- Database untuk menyimpan data profil Farcaster users

CREATE DATABASE IF NOT EXISTS warpsync;
USE warpsync;

-- Tabel untuk menyimpan data profil Farcaster users
CREATE TABLE IF NOT EXISTS farcaster_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    fid VARCHAR(50) NOT NULL UNIQUE,
    username VARCHA<PERSON>(100) NOT NULL,
    display_name <PERSON><PERSON><PERSON><PERSON>(200),
    bio TEXT,
    followers_count INT DEFAULT 0,
    following_count INT DEFAULT 0,
    custody_address VARCHAR(100),
    verified_eth_addresses JSON,
    verified_sol_addresses JSON,
    primary_eth_address VARCHAR(100),
    primary_sol_address VARCHAR(100),
    pfp_url TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_fetched TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    INDEX idx_fid (fid),
    INDEX idx_username (username),
    INDEX idx_created_at (created_at),
    INDEX idx_last_fetched (last_fetched)
);

-- Tabel untuk log aktivitas (opsional untuk tracking)
CREATE TABLE IF NOT EXISTS activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    action_type ENUM('ADD_USER', 'REFRESH_USER', 'DELETE_USER') NOT NULL,
    fid VARCHAR(50),
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_action_type (action_type),
    INDEX idx_fid (fid),
    INDEX idx_created_at (created_at)
);

-- Tabel untuk konfigurasi aplikasi
CREATE TABLE IF NOT EXISTS app_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default settings
INSERT INTO app_settings (setting_key, setting_value, description) VALUES
('api_rate_limit', '100', 'Maximum API calls per hour'),
('items_per_page', '12', 'Number of user cards per page'),
('cache_duration', '3600', 'Cache duration in seconds'),
('app_name', 'WarpSync', 'Application name'),
('app_version', '1.0.0', 'Application version');

-- Sample data untuk testing (opsional)
-- INSERT INTO farcaster_users (fid, username, display_name, bio, followers_count, following_count, custody_address, pfp_url) VALUES
-- ('1', 'dwr', 'Dan Romero', 'Co-founder of Farcaster', 50000, 1000, '0x123...abc', 'https://example.com/pfp1.jpg'),
-- ('2', 'v', 'Varun Srinivasan', 'Building Farcaster', 30000, 800, '0x456...def', 'https://example.com/pfp2.jpg');
