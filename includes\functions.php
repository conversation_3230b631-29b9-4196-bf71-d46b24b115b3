<?php

/**
 * WarpSync Helper Functions
 * Functions untuk operasi umum aplikasi
 */

// Check if WARPSYNC_APP is already defined to avoid redefinition warning
if (!defined('WARPSYNC_APP')) {
    define('WARPSYNC_APP', true);
}
require_once __DIR__ . '/../config/config.php';

/**
 * Farcaster API Functions
 */

/**
 * Fetch user data from Neynar API by FID
 */
function fetchFarcasterUser($fid)
{
    if (!validateFID($fid)) {
        throw new Exception("Invalid FID format");
    }

    // Use Neynar API endpoint for bulk users (can handle single FID too)
    $url = FARCASTER_API_BASE . "user/bulk?fids=" . $fid;

    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => API_TIMEOUT,
        CURLOPT_HTTPHEADER => [
            'Accept: application/json',
            'x-api-key: ' . NEYNAR_API_KEY,
            'User-Agent: WarpSync/1.0'
        ],
        CURLOPT_SSL_VERIFYPEER => false
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        throw new Exception("cURL Error: " . $error);
    }

    if ($httpCode !== 200) {
        throw new Exception("API Error: HTTP " . $httpCode . " - Response: " . $response);
    }

    $data = json_decode($response, true);

    if (!$data || !isset($data['users']) || empty($data['users'])) {
        throw new Exception("User not found or invalid API response");
    }

    // Return the first (and only) user from the response
    return $data['users'][0];
}

/**
 * Database Operations for Farcaster Users
 */

/**
 * Save or update user data in database
 */
function saveUserToDatabase($userData)
{
    try {
        // Check if user already exists
        $existingUser = fetchOne(
            "SELECT id FROM farcaster_users WHERE fid = ?",
            [$userData['fid']]
        );

        // Extract data from Neynar API response format
        $bio = '';
        if (isset($userData['profile']) && isset($userData['profile']['bio']) && isset($userData['profile']['bio']['text'])) {
            $bio = $userData['profile']['bio']['text'];
        }

        $custody_address = $userData['custody_address'] ?? '';
        $pfp_url = $userData['pfp_url'] ?? '';
        $followers_count = $userData['follower_count'] ?? 0;
        $following_count = $userData['following_count'] ?? 0;
        $display_name = $userData['display_name'] ?? '';

        // Extract multiple verified addresses
        $verified_eth_addresses = null;
        $verified_sol_addresses = null;
        $primary_eth_address = '';
        $primary_sol_address = '';

        if (isset($userData['verified_addresses'])) {
            $verified_addresses = $userData['verified_addresses'];

            // ETH addresses
            if (isset($verified_addresses['eth_addresses']) && is_array($verified_addresses['eth_addresses'])) {
                $verified_eth_addresses = json_encode($verified_addresses['eth_addresses']);
            }

            // SOL addresses
            if (isset($verified_addresses['sol_addresses']) && is_array($verified_addresses['sol_addresses'])) {
                $verified_sol_addresses = json_encode($verified_addresses['sol_addresses']);
            }

            // Primary addresses
            if (isset($verified_addresses['primary'])) {
                $primary_eth_address = $verified_addresses['primary']['eth_address'] ?? '';
                $primary_sol_address = $verified_addresses['primary']['sol_address'] ?? '';
            }
        }

        if ($existingUser) {
            // Update existing user
            $sql = "UPDATE farcaster_users SET
                    username = ?,
                    display_name = ?,
                    bio = ?,
                    followers_count = ?,
                    following_count = ?,
                    custody_address = ?,
                    verified_eth_addresses = ?,
                    verified_sol_addresses = ?,
                    primary_eth_address = ?,
                    primary_sol_address = ?,
                    pfp_url = ?,
                    last_fetched = NOW(),
                    updated_at = NOW()
                    WHERE fid = ?";

            $params = [
                $userData['username'],
                $display_name,
                $bio,
                $followers_count,
                $following_count,
                $custody_address,
                $verified_eth_addresses,
                $verified_sol_addresses,
                $primary_eth_address,
                $primary_sol_address,
                $pfp_url,
                $userData['fid']
            ];

            updateData($sql, $params);
            logActivity('REFRESH_USER', $userData['fid'], 'User data updated');

            return ['action' => 'updated', 'fid' => $userData['fid']];
        } else {
            // Insert new user
            $sql = "INSERT INTO farcaster_users
                    (fid, username, display_name, bio, followers_count, following_count,
                     custody_address, verified_eth_addresses, verified_sol_addresses,
                     primary_eth_address, primary_sol_address, pfp_url, last_fetched)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

            $params = [
                $userData['fid'],
                $userData['username'],
                $display_name,
                $bio,
                $followers_count,
                $following_count,
                $custody_address,
                $verified_eth_addresses,
                $verified_sol_addresses,
                $primary_eth_address,
                $primary_sol_address,
                $pfp_url
            ];

            $insertId = insertData($sql, $params);
            logActivity('ADD_USER', $userData['fid'], 'New user added');

            return ['action' => 'inserted', 'fid' => $userData['fid'], 'id' => $insertId];
        }
    } catch (Exception $e) {
        error_log("Error saving user to database: " . $e->getMessage());
        throw new Exception("Failed to save user data: " . $e->getMessage());
    }
}

/**
 * Get all users from database with pagination
 */
function getAllUsers($page = 1, $limit = ITEMS_PER_PAGE, $search = '')
{
    $offset = ($page - 1) * $limit;

    $whereClause = '';
    $params = [];

    if (!empty($search)) {
        $whereClause = "WHERE username LIKE ? OR display_name LIKE ?";
        $searchTerm = '%' . $search . '%';
        $params = [$searchTerm, $searchTerm];
    }

    // Get total count
    $countSql = "SELECT COUNT(*) as total FROM farcaster_users " . $whereClause;
    $totalResult = fetchOne($countSql, $params);
    $total = $totalResult['total'];

    // Get users
    $sql = "SELECT * FROM farcaster_users " . $whereClause . " 
            ORDER BY updated_at DESC LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;

    $users = fetchAll($sql, $params);

    return [
        'users' => $users,
        'total' => $total,
        'page' => $page,
        'limit' => $limit,
        'total_pages' => ceil($total / $limit)
    ];
}

/**
 * Get single user by FID
 */
function getUserByFID($fid)
{
    return fetchOne("SELECT * FROM farcaster_users WHERE fid = ?", [$fid]);
}

/**
 * Delete user by FID
 */
function deleteUserByFID($fid)
{
    $result = updateData("DELETE FROM farcaster_users WHERE fid = ?", [$fid]);
    if ($result > 0) {
        logActivity('DELETE_USER', $fid, 'User deleted');
    }
    return $result;
}

/**
 * Utility Functions
 */

/**
 * Process and fetch user by FID
 */
function processUserByFID($fid)
{
    try {
        // Fetch from API
        $apiData = fetchFarcasterUser($fid);

        // Save to database
        $result = saveUserToDatabase($apiData);

        return [
            'success' => true,
            'message' => 'User processed successfully',
            'data' => $result
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => $e->getMessage(),
            'data' => null
        ];
    }
}

/**
 * Refresh user data by FID
 */
function refreshUserData($fid)
{
    return processUserByFID($fid);
}

/**
 * Get app statistics
 */
function getAppStats()
{
    $stats = [];

    // Total users
    $result = fetchOne("SELECT COUNT(*) as total FROM farcaster_users");
    $stats['total_users'] = $result['total'];

    // Users added today
    $result = fetchOne("SELECT COUNT(*) as total FROM farcaster_users WHERE DATE(created_at) = CURDATE()");
    $stats['users_today'] = $result['total'];

    // Last update
    $result = fetchOne("SELECT MAX(last_fetched) as last_update FROM farcaster_users");
    $stats['last_update'] = $result['last_update'];

    // Recent activity
    $stats['recent_activity'] = fetchAll(
        "SELECT * FROM activity_logs ORDER BY created_at DESC LIMIT 10"
    );

    return $stats;
}
