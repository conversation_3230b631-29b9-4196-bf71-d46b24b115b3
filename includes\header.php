<?php

/**
 * Header Template
 * Template untuk bagian header website
 */

if (!defined('WARPSYNC_APP')) {
    die('Direct access not permitted');
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' . APP_NAME : APP_NAME; ?></title>

    <!-- Favicon -->
    <link rel="icon" href="<?php echo APP_URL; ?>assets/images/favicon.ico" type="image/x-icon">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Space+Grotesk:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- CSS Files -->
    <link rel="stylesheet" href="<?php echo APP_URL; ?>assets/css/style.css">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>

<body class="<?php echo isset($bodyClass) ? $bodyClass : ''; ?>">
    <header class="main-header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <a href="<?php echo APP_URL; ?>">
                        <span class="logo-icon"><i class="fas fa-sync-alt"></i></span>
                        <span class="logo-text"><?php echo APP_NAME; ?></span>
                    </a>
                </div>

                <nav class="main-nav">
                    <ul>
                        <li><a href="<?php echo APP_URL; ?>" class="<?php echo !isset($currentPage) || $currentPage === 'home' ? 'active' : ''; ?>">Home</a></li>
                        <li><a href="<?php echo APP_URL; ?>add_user.php" class="<?php echo isset($currentPage) && $currentPage === 'add_user' ? 'active' : ''; ?>">Add User</a></li>
                    </ul>
                </nav>

                <div class="mobile-menu-toggle">
                    <button id="mobile-menu-btn">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <div class="mobile-menu">
        <div class="mobile-menu-header">
            <div class="logo">
                <span class="logo-icon"><i class="fas fa-sync-alt"></i></span>
                <span class="logo-text"><?php echo APP_NAME; ?></span>
            </div>
            <button id="mobile-menu-close">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <nav class="mobile-nav">
            <ul>
                <li><a href="<?php echo APP_URL; ?>">Home</a></li>
                <li><a href="<?php echo APP_URL; ?>add_user.php">Add User</a></li>
            </ul>
        </nav>
    </div>

    <main class="main-content">
        <?php if (isset($pageHeader) && $pageHeader): ?>
            <div class="page-header">
                <div class="container">
                    <h1><?php echo $pageHeader; ?></h1>
                    <?php if (isset($pageDescription)): ?>
                        <p class="page-description"><?php echo $pageDescription; ?></p>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>