<?php
/**
 * WarpSync CLI Setup Script
 * Run this via command line: php setup.php
 */

echo "\n";
echo "🚀 WarpSync Database Setup\n";
echo "==========================\n\n";

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';  // Change if you have MySQL password
$database_name = 'warpsync';

try {
    echo "📡 Connecting to MySQL server...\n";
    
    // Connect to MySQL server (without database)
    $dsn = "mysql:host=$host;charset=utf8mb4";
    $options = [
        PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES   => false,
    ];
    
    $pdo = new PDO($dsn, $username, $password, $options);
    echo "✅ Connected to MySQL server successfully!\n\n";
    
    // Create database
    echo "🗄️  Creating database '$database_name'...\n";
    $sql = "CREATE DATABASE IF NOT EXISTS `$database_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
    $pdo->exec($sql);
    echo "✅ Database '$database_name' created!\n\n";
    
    // Use the database
    $pdo->exec("USE `$database_name`");
    echo "📂 Selected database '$database_name'\n\n";
    
    // Create farcaster_users table
    echo "📋 Creating table 'farcaster_users'...\n";
    $sql_users = "
    CREATE TABLE IF NOT EXISTS `farcaster_users` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `fid` varchar(50) NOT NULL,
        `username` varchar(100) NOT NULL,
        `display_name` varchar(200) DEFAULT NULL,
        `bio` text DEFAULT NULL,
        `followers_count` int(11) DEFAULT 0,
        `following_count` int(11) DEFAULT 0,
        `custody_address` varchar(100) DEFAULT NULL,
        `pfp_url` text DEFAULT NULL,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        `last_fetched` timestamp NOT NULL DEFAULT current_timestamp(),
        `is_active` tinyint(1) DEFAULT 1,
        PRIMARY KEY (`id`),
        UNIQUE KEY `fid` (`fid`),
        KEY `idx_fid` (`fid`),
        KEY `idx_username` (`username`),
        KEY `idx_created_at` (`created_at`),
        KEY `idx_last_fetched` (`last_fetched`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql_users);
    echo "✅ Table 'farcaster_users' created!\n";
    
    // Create activity_logs table
    echo "📋 Creating table 'activity_logs'...\n";
    $sql_logs = "
    CREATE TABLE IF NOT EXISTS `activity_logs` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `action_type` enum('ADD_USER','REFRESH_USER','DELETE_USER') NOT NULL,
        `fid` varchar(50) DEFAULT NULL,
        `details` text DEFAULT NULL,
        `ip_address` varchar(45) DEFAULT NULL,
        `user_agent` text DEFAULT NULL,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        PRIMARY KEY (`id`),
        KEY `idx_action_type` (`action_type`),
        KEY `idx_fid` (`fid`),
        KEY `idx_created_at` (`created_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql_logs);
    echo "✅ Table 'activity_logs' created!\n";
    
    // Create app_settings table
    echo "📋 Creating table 'app_settings'...\n";
    $sql_settings = "
    CREATE TABLE IF NOT EXISTS `app_settings` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `setting_key` varchar(100) NOT NULL,
        `setting_value` text DEFAULT NULL,
        `description` text DEFAULT NULL,
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`id`),
        UNIQUE KEY `setting_key` (`setting_key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql_settings);
    echo "✅ Table 'app_settings' created!\n\n";
    
    // Insert default settings
    echo "⚙️  Inserting default settings...\n";
    $default_settings = [
        ['api_rate_limit', '100', 'Maximum API calls per hour'],
        ['items_per_page', '12', 'Number of user cards per page'],
        ['cache_duration', '3600', 'Cache duration in seconds'],
        ['app_name', 'WarpSync', 'Application name'],
        ['app_version', '1.0.0', 'Application version']
    ];
    
    $sql_insert = "INSERT IGNORE INTO app_settings (setting_key, setting_value, description) VALUES (?, ?, ?)";
    $stmt = $pdo->prepare($sql_insert);
    
    foreach ($default_settings as $setting) {
        $stmt->execute($setting);
        echo "  ✓ {$setting[0]}: {$setting[1]}\n";
    }
    
    echo "\n🧪 Testing setup...\n";
    
    // Test queries
    $test_queries = [
        "SELECT COUNT(*) as count FROM farcaster_users" => "farcaster_users",
        "SELECT COUNT(*) as count FROM activity_logs" => "activity_logs", 
        "SELECT COUNT(*) as count FROM app_settings" => "app_settings"
    ];
    
    foreach ($test_queries as $query => $table) {
        try {
            $stmt = $pdo->query($query);
            $result = $stmt->fetch();
            echo "  ✓ Table '$table': {$result['count']} records\n";
        } catch (Exception $e) {
            echo "  ✗ Error testing table '$table': " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n🎉 Setup completed successfully!\n";
    echo "==================================\n";
    echo "Database: $database_name\n";
    echo "Tables: 3 created\n";
    echo "Settings: 5 configured\n\n";
    
    echo "🚀 Next steps:\n";
    echo "1. Open your browser: http://localhost/farcaster/\n";
    echo "2. Go to admin panel: http://localhost/farcaster/admin/\n";
    echo "3. Add your first user: http://localhost/farcaster/admin/add_user.php\n";
    echo "4. Test connection: php test.php\n\n";
    
} catch (PDOException $e) {
    echo "\n❌ Setup failed!\n";
    echo "Error: " . $e->getMessage() . "\n\n";
    
    echo "💡 Common solutions:\n";
    echo "1. Make sure XAMPP MySQL is running\n";
    echo "2. Check MySQL username/password in this script\n";
    echo "3. Try: net start mysql (Windows) or sudo service mysql start (Linux)\n";
    echo "4. Access phpMyAdmin: http://localhost/phpmyadmin\n\n";
    
} catch (Exception $e) {
    echo "\n❌ Unexpected error!\n";
    echo "Error: " . $e->getMessage() . "\n\n";
}

echo "WarpSync CLI Setup v1.0.0\n";
echo "========================\n";
?>
