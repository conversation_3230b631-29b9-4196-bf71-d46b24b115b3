<?php
/**
 * Simple API Debug - No includes
 * Test API langsung tanpa include file lain
 */
?>
<!DOCTYPE html>
<html>
<head>
    <title>Simple API Debug</title>
    <style>
        body { font-family: Arial; max-width: 1000px; margin: 0 auto; padding: 20px; background: #f5f5f5; }
        .success { color: green; padding: 10px; border: 1px solid green; margin: 10px 0; background: #f0fff0; }
        .error { color: red; padding: 10px; border: 1px solid red; margin: 10px 0; background: #fff0f0; }
        .warning { color: orange; padding: 10px; border: 1px solid orange; margin: 10px 0; background: #fff8f0; }
        .info { color: blue; padding: 10px; border: 1px solid blue; margin: 10px 0; background: #f0f8ff; }
        pre { background: #f8f8f8; padding: 10px; overflow: auto; max-height: 300px; font-size: 12px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f2f2f2; }
        code { background: #f0f0f0; padding: 2px 4px; border-radius: 3px; }
    </style>
</head>
<body>

<h1>🔍 Simple API Debug</h1>

<?php
// Test FID
$test_fid = 3; // Dan Romero

echo "<h2>Testing Neynar API with FID: $test_fid</h2>";

// API URL
$url = "https://api.neynar.com/v2/farcaster/user/bulk?fids=" . $test_fid;

echo "<div class='info'>";
echo "<strong>API URL:</strong> <code>$url</code><br>";
echo "<strong>Method:</strong> GET<br>";
echo "<strong>Headers:</strong> x-api-key: NEYNAR_API_DOCS";
echo "</div>";

// Test dengan cURL
echo "<h3>🌐 cURL Test</h3>";

$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_HTTPHEADER => [
        'Accept: application/json',
        'x-api-key: NEYNAR_API_DOCS',
        'User-Agent: WarpSync-Debug/1.0'
    ],
    CURLOPT_SSL_VERIFYPEER => false
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
$error = curl_error($ch);
curl_close($ch);

// Show response info
echo "<table>";
echo "<tr><th>Property</th><th>Value</th></tr>";
echo "<tr><td>HTTP Code</td><td><strong>$httpCode</strong></td></tr>";
echo "<tr><td>Content Type</td><td><strong>$contentType</strong></td></tr>";
echo "<tr><td>cURL Error</td><td><strong>" . ($error ?: 'None') . "</strong></td></tr>";
echo "<tr><td>Response Length</td><td><strong>" . strlen($response) . " bytes</strong></td></tr>";
echo "</table>";

// Check HTTP status
if ($httpCode !== 200) {
    echo "<div class='error'>";
    echo "<strong>❌ HTTP Error $httpCode</strong><br>";
    echo "API returned non-200 status. Response:<br>";
    echo "<pre>" . htmlspecialchars(substr($response, 0, 500)) . "</pre>";
    echo "</div>";
} else {
    echo "<div class='success'>";
    echo "<strong>✅ HTTP 200 OK</strong> - API responded successfully";
    echo "</div>";
}

// Check content type
if ($contentType && !strpos($contentType, 'application/json')) {
    echo "<div class='error'>";
    echo "<strong>❌ Wrong Content Type</strong><br>";
    echo "Expected: application/json<br>";
    echo "Got: $contentType<br>";
    echo "This means API returned HTML/text instead of JSON.";
    echo "</div>";
} else {
    echo "<div class='success'>";
    echo "<strong>✅ Correct Content Type</strong> - application/json";
    echo "</div>";
}

// Try to parse JSON
echo "<h3>🔍 JSON Analysis</h3>";

$data = json_decode($response, true);
$json_error = json_last_error();

if ($json_error !== JSON_ERROR_NONE) {
    echo "<div class='error'>";
    echo "<strong>❌ JSON Parse Error:</strong> " . json_last_error_msg() . "<br>";
    echo "<strong>Raw Response (first 500 chars):</strong><br>";
    echo "<pre>" . htmlspecialchars(substr($response, 0, 500)) . "</pre>";
    echo "</div>";
} else {
    echo "<div class='success'>";
    echo "<strong>✅ Valid JSON</strong> - Response parsed successfully";
    echo "</div>";
    
    // Show JSON structure
    echo "<h4>JSON Structure:</h4>";
    echo "<pre>" . htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT)) . "</pre>";
    
    // Extract user data
    if (isset($data['users']) && !empty($data['users'])) {
        $user = $data['users'][0];
        
        echo "<h4>📋 Extracted User Data:</h4>";
        echo "<table>";
        echo "<tr><th>Field</th><th>Value</th></tr>";
        
        $fields = [
            'fid' => $user['fid'] ?? 'N/A',
            'username' => $user['username'] ?? 'N/A',
            'display_name' => $user['display_name'] ?? 'N/A',
            'follower_count' => number_format($user['follower_count'] ?? 0),
            'following_count' => number_format($user['following_count'] ?? 0),
            'custody_address' => $user['custody_address'] ?? 'N/A',
            'pfp_url' => isset($user['pfp_url']) ? 'Available' : 'N/A',
            'bio' => isset($user['profile']['bio']['text']) ? substr($user['profile']['bio']['text'], 0, 100) . '...' : 'N/A'
        ];
        
        foreach ($fields as $field => $value) {
            echo "<tr>";
            echo "<td><strong>$field</strong></td>";
            echo "<td>" . htmlspecialchars($value) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<div class='success'>";
        echo "<strong>✅ User Data Found</strong> - All required fields available";
        echo "</div>";
        
    } else {
        echo "<div class='warning'>";
        echo "<strong>⚠️ No Users in Response</strong><br>";
        echo "JSON is valid but no users found in 'users' array.";
        echo "</div>";
    }
}

// Test different FIDs
echo "<h3>🧪 Test Multiple FIDs</h3>";

$test_fids = [1, 2, 3, 194, 191];

foreach ($test_fids as $fid) {
    $test_url = "https://api.neynar.com/v2/farcaster/user/bulk?fids=" . $fid;
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $test_url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_HTTPHEADER => [
            'Accept: application/json',
            'x-api-key: NEYNAR_API_DOCS'
        ],
        CURLOPT_SSL_VERIFYPEER => false
    ]);
    
    $test_response = curl_exec($ch);
    $test_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($test_code === 200) {
        $test_data = json_decode($test_response, true);
        if ($test_data && isset($test_data['users']) && !empty($test_data['users'])) {
            $test_user = $test_data['users'][0];
            echo "<div class='success'>";
            echo "<strong>✅ FID $fid:</strong> " . htmlspecialchars($test_user['username'] ?? 'Unknown') . " - " . htmlspecialchars($test_user['display_name'] ?? 'No name');
            echo "</div>";
        } else {
            echo "<div class='warning'>";
            echo "<strong>⚠️ FID $fid:</strong> No user data found";
            echo "</div>";
        }
    } else {
        echo "<div class='error'>";
        echo "<strong>❌ FID $fid:</strong> HTTP $test_code error";
        echo "</div>";
    }
}

// Recommendations
echo "<h3>💡 Diagnosis & Next Steps</h3>";

if ($httpCode === 200 && $json_error === JSON_ERROR_NONE && isset($data['users']) && !empty($data['users'])) {
    echo "<div class='success'>";
    echo "<strong>🎉 API is working perfectly!</strong><br><br>";
    echo "<strong>Next steps:</strong><br>";
    echo "1. API is returning valid JSON with user data<br>";
    echo "2. The error 'Unexpected token &lt;' is likely in your frontend JavaScript<br>";
    echo "3. Check browser console for JavaScript errors<br>";
    echo "4. Try inserting sample data: <a href='insert_sample_data.php'>insert_sample_data.php</a><br>";
    echo "5. Test frontend with sample data: <a href='index.php'>index.php</a>";
    echo "</div>";
} else {
    echo "<div class='error'>";
    echo "<strong>❌ API Issues Detected</strong><br><br>";
    
    if ($httpCode !== 200) {
        echo "• HTTP Error: API not responding with 200 OK<br>";
    }
    
    if ($json_error !== JSON_ERROR_NONE) {
        echo "• JSON Error: API returning invalid JSON<br>";
    }
    
    if (!isset($data['users']) || empty($data['users'])) {
        echo "• Data Error: No users in API response<br>";
    }
    
    echo "<br><strong>Solutions:</strong><br>";
    echo "1. Check internet connection<br>";
    echo "2. Try different API key<br>";
    echo "3. Use sample data instead: <a href='insert_sample_data.php'>insert_sample_data.php</a>";
    echo "</div>";
}

echo "<h3>🔗 Quick Links</h3>";
echo "<p>";
echo "<a href='insert_sample_data.php'>Insert Sample Data</a> | ";
echo "<a href='index.php'>Main App</a> | ";
echo "<a href='admin/add_user.php'>Add User</a> | ";
echo "<a href='test_connection.php'>Database Test</a>";
echo "</p>";

?>

</body>
</html>
