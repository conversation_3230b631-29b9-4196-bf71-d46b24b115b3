/* WarpSync - Modern Futuristic CSS */

/* CSS Variables */
:root {
    /* Colors - Futuristic Theme */
    --primary-color: #00d4ff;
    --primary-dark: #0099cc;
    --secondary-color: #ff6b35;
    --accent-color: #7c3aed;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    /* Background Colors */
    --bg-primary: #0a0a0f;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --bg-card: #1e293b;
    --bg-overlay: rgba(10, 10, 15, 0.9);
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --text-dark: #1e293b;
    
    /* Border Colors */
    --border-color: #334155;
    --border-light: #475569;
    --border-accent: #00d4ff;
    
    /* Fonts */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-heading: 'Space Grotesk', sans-serif;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-glow: 0 0 20px rgba(0, 212, 255, 0.3);
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Background Pattern */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 50%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(124, 58, 237, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(255, 107, 53, 0.1) 0%, transparent 50%);
    z-index: -1;
    pointer-events: none;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Header */
.main-header {
    background: rgba(26, 26, 46, 0.9);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) 0;
}

.logo a {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--text-primary);
    font-family: var(--font-heading);
    font-weight: 700;
    font-size: 1.5rem;
}

.logo-icon {
    margin-right: var(--spacing-sm);
    color: var(--primary-color);
    animation: rotate 3s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.main-nav ul {
    display: flex;
    list-style: none;
    gap: var(--spacing-lg);
}

.main-nav a {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
    position: relative;
}

.main-nav a:hover,
.main-nav a.active {
    color: var(--primary-color);
    background: rgba(0, 212, 255, 0.1);
}

.main-nav a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition-normal);
    transform: translateX(-50%);
}

.main-nav a:hover::after,
.main-nav a.active::after {
    width: 80%;
}

/* Mobile Menu */
.mobile-menu-toggle {
    display: none;
}

.mobile-menu {
    display: none;
    position: fixed;
    top: 0;
    right: -100%;
    width: 300px;
    height: 100vh;
    background: var(--bg-secondary);
    backdrop-filter: blur(10px);
    border-left: 1px solid var(--border-color);
    z-index: 2000;
    transition: var(--transition-normal);
}

.mobile-menu.active {
    right: 0;
}

.mobile-menu-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.mobile-nav ul {
    list-style: none;
    padding: var(--spacing-lg);
}

.mobile-nav li {
    margin-bottom: var(--spacing-md);
}

.mobile-nav a {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    display: block;
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
}

.mobile-nav a:hover {
    color: var(--primary-color);
    background: rgba(0, 212, 255, 0.1);
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-2xl) 0;
    text-align: center;
}

.page-header h1 {
    font-family: var(--font-heading);
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-description {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Main Content */
.main-content {
    min-height: calc(100vh - 200px);
    padding: var(--spacing-2xl) 0;
}

/* Search & Controls */
.search-controls {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
    box-shadow: var(--shadow-lg);
}

.search-form {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
    flex-wrap: wrap;
}

.search-input {
    flex: 1;
    min-width: 250px;
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 1rem;
    transition: var(--transition-normal);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
}

.search-input::placeholder {
    color: var(--text-muted);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: var(--transition-normal);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--text-dark);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
    color: white;
}

.btn-error {
    background: linear-gradient(135deg, var(--error-color) 0%, #dc2626 100%);
    color: white;
    transition: all 0.3s ease;
}

.btn-error:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
}

.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.875rem;
}

.btn-lg {
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: 1.125rem;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.btn i {
    margin-right: var(--spacing-sm);
}

/* User Cards Grid */
.users-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

/* User Card */
.user-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.user-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--secondary-color));
    opacity: 0;
    transition: var(--transition-normal);
}

.user-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.user-card:hover::before {
    opacity: 1;
}

.user-card-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: var(--spacing-md);
    border: 2px solid var(--border-color);
    transition: var(--transition-normal);
}

.user-card:hover .user-avatar {
    border-color: var(--primary-color);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

.user-info h3 {
    font-family: var(--font-heading);
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.user-info .username {
    color: var(--primary-color);
    font-size: 0.9rem;
    font-weight: 500;
}

.user-stats {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    font-family: var(--font-heading);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-muted);
}

.user-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

/* User Details (Expanded) */
.user-details {
    max-height: 0;
    overflow: hidden;
    transition: max-height var(--transition-slow);
}

.user-details.expanded {
    max-height: 500px;
}

.user-bio {
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin: var(--spacing-md) 0;
    border-left: 3px solid var(--primary-color);
}

.user-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.meta-item {
    background: var(--bg-secondary);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.meta-label {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: var(--spacing-xs);
}

.meta-value {
    font-weight: 500;
    color: var(--text-primary);
    word-break: break-all;
}

.address-item {
    font-family: monospace;
    font-size: 0.8rem;
    background: var(--bg-primary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    margin: var(--spacing-xs) 0;
    border: 1px solid var(--border-color);
    transition: var(--transition-normal);
}

.address-item:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
    cursor: pointer;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-nav {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .mobile-menu {
        display: block;
    }
    
    .page-header h1 {
        font-size: 2rem;
    }
    
    .users-grid {
        grid-template-columns: 1fr;
    }
    
    .search-form {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-input {
        min-width: auto;
    }
    
    .user-stats {
        justify-content: space-around;
    }
    
    .user-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    .user-card {
        padding: var(--spacing-md);
    }

    .user-card-header {
        flex-direction: column;
        text-align: center;
    }

    .user-avatar {
        margin-right: 0;
        margin-bottom: var(--spacing-sm);
    }
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-overlay);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.spinner {
    width: 60px;
    height: 60px;
    position: relative;
}

.double-bounce1, .double-bounce2 {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: var(--primary-color);
    opacity: 0.6;
    position: absolute;
    top: 0;
    left: 0;
    animation: sk-bounce 2.0s infinite ease-in-out;
}

.double-bounce2 {
    animation-delay: -1.0s;
}

@keyframes sk-bounce {
    0%, 100% {
        transform: scale(0);
    } 50% {
        transform: scale(1);
    }
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
}

.toast {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    box-shadow: var(--shadow-xl);
    transform: translateX(100%);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.toast.show {
    transform: translateX(0);
}

.toast::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-color);
}

.toast.success::before {
    background: var(--success-color);
}

.toast.warning::before {
    background: var(--warning-color);
}

.toast.error::before {
    background: var(--error-color);
}

.toast-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
}

.toast-title {
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
}

.toast-title i {
    margin-right: var(--spacing-sm);
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: 1.2rem;
    transition: var(--transition-fast);
}

.toast-close:hover {
    color: var(--text-primary);
}

.toast-message {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-2xl);
}

.pagination-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-normal);
    min-width: 40px;
    text-align: center;
}

.pagination-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.pagination-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--text-dark);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin: 0 var(--spacing-md);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-muted);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    color: var(--border-color);
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

.empty-state p {
    font-size: 1rem;
    margin-bottom: var(--spacing-lg);
}

/* Footer */
.main-footer {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-2xl) 0;
    margin-top: var(--spacing-2xl);
}

.footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
}

.footer-logo {
    display: flex;
    align-items: center;
    font-family: var(--font-heading);
    font-weight: 700;
    font-size: 1.25rem;
}

.footer-logo .logo-icon {
    margin-right: var(--spacing-sm);
    color: var(--primary-color);
}

.footer-info {
    text-align: center;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.footer-info a {
    color: var(--primary-color);
    text-decoration: none;
}

.footer-info a:hover {
    text-decoration: underline;
}

.footer-links ul {
    display: flex;
    list-style: none;
    gap: var(--spacing-lg);
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-normal);
}

.footer-links a:hover {
    color: var(--primary-color);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.hidden { display: none; }
.visible { display: block; }

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}
