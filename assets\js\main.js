/**
 * WarpSync Main JavaScript
 * Handles user interactions, API calls, and UI updates
 */

class WarpSync {
    constructor() {
        this.apiBase = window.location.origin + window.location.pathname.replace(/\/[^\/]*$/, '') + '/api/';
        this.currentPage = 1;
        this.currentSearch = '';
        this.isLoading = false;
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadUsers();
    }
    
    bindEvents() {
        // Mobile menu toggle
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const mobileMenuClose = document.getElementById('mobile-menu-close');
        const mobileMenu = document.querySelector('.mobile-menu');
        
        if (mobileMenuBtn) {
            mobileMenuBtn.addEventListener('click', () => {
                mobileMenu.classList.add('active');
            });
        }
        
        if (mobileMenuClose) {
            mobileMenuClose.addEventListener('click', () => {
                mobileMenu.classList.remove('active');
            });
        }
        
        // Search functionality
        const searchInput = document.getElementById('search-input');
        const searchBtn = document.getElementById('search-btn');
        
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.searchUsers(e.target.value);
                }, 500);
            });
            
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.searchUsers(e.target.value);
                }
            });
        }
        
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                const query = searchInput ? searchInput.value : '';
                this.searchUsers(query);
            });
        }
        
        // Refresh all button
        const refreshAllBtn = document.getElementById('refresh-all-btn');
        if (refreshAllBtn) {
            refreshAllBtn.addEventListener('click', () => {
                this.refreshAllUsers();
            });
        }
        
        // Close toast notifications
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('toast-close')) {
                this.closeToast(e.target.closest('.toast'));
            }
        });
    }
    
    // API Methods
    async apiCall(endpoint, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
        };
        
        const config = { ...defaultOptions, ...options };
        
        try {
            const response = await fetch(this.apiBase + endpoint, config);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || 'API request failed');
            }
            
            return data;
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    }
    
    // Load users with pagination and search
    async loadUsers(page = 1, search = '') {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading();
        
        try {
            const params = new URLSearchParams({
                page: page,
                search: search,
                limit: 12
            });
            
            const response = await this.apiCall(`search_users.php?${params}`);
            
            if (response.success) {
                this.renderUsers(response.data.users);
                this.renderPagination(response.data.pagination);
                this.currentPage = page;
                this.currentSearch = search;
            } else {
                this.showToast('Error loading users: ' + response.message, 'error');
            }
        } catch (error) {
            this.showToast('Failed to load users: ' + error.message, 'error');
        } finally {
            this.isLoading = false;
            this.hideLoading();
        }
    }
    
    // Search users
    async searchUsers(query) {
        this.loadUsers(1, query);
    }
    
    // Refresh single user
    async refreshUser(fid) {
        try {
            this.showLoading();
            
            const response = await this.apiCall('refresh_user.php', {
                method: 'POST',
                body: JSON.stringify({ fid: fid })
            });
            
            if (response.success) {
                this.showToast('User data refreshed successfully', 'success');
                this.loadUsers(this.currentPage, this.currentSearch);
            } else {
                this.showToast('Failed to refresh user: ' + response.message, 'error');
            }
        } catch (error) {
            this.showToast('Error refreshing user: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    // Refresh all users
    async refreshAllUsers() {
        if (!confirm('This will refresh all users data. This may take a while. Continue?')) {
            return;
        }
        
        try {
            this.showLoading();
            
            const response = await this.apiCall('refresh_user.php?all=1', {
                method: 'POST'
            });
            
            if (response.success) {
                this.showToast('All users refreshed successfully', 'success');
                this.loadUsers(this.currentPage, this.currentSearch);
            } else {
                this.showToast('Some users failed to refresh: ' + response.message, 'warning');
            }
        } catch (error) {
            this.showToast('Error refreshing users: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    // Delete user
    async deleteUser(fid) {
        if (!confirm('Are you sure you want to delete this user?')) {
            return;
        }
        
        try {
            this.showLoading();
            
            const response = await this.apiCall('search_users.php', {
                method: 'DELETE',
                body: JSON.stringify({ fid: fid })
            });
            
            if (response.success) {
                this.showToast('User deleted successfully', 'success');
                this.loadUsers(this.currentPage, this.currentSearch);
            } else {
                this.showToast('Failed to delete user: ' + response.message, 'error');
            }
        } catch (error) {
            this.showToast('Error deleting user: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    // Render users grid
    renderUsers(users) {
        const container = document.getElementById('users-container');
        if (!container) return;
        
        if (users.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-users"></i>
                    <h3>No users found</h3>
                    <p>Try adjusting your search criteria or add some users.</p>
                    <a href="admin/add_user.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add User
                    </a>
                </div>
            `;
            return;
        }
        
        container.innerHTML = users.map(user => this.createUserCard(user)).join('');
        
        // Bind card events
        this.bindCardEvents();
    }
    
    // Create user card HTML
    createUserCard(user) {
        const avatarUrl = user.pfp_url || 'https://via.placeholder.com/60x60/334155/cbd5e1?text=' + (user.username.charAt(0).toUpperCase());
        
        return `
            <div class="user-card fade-in" data-fid="${user.fid}">
                <div class="user-card-header">
                    <img src="${avatarUrl}" alt="${user.display_name}" class="user-avatar" 
                         onerror="this.src='https://via.placeholder.com/60x60/334155/cbd5e1?text=${user.username.charAt(0).toUpperCase()}'">
                    <div class="user-info">
                        <h3>${user.display_name || user.username}</h3>
                        <div class="username">@${user.username}</div>
                    </div>
                </div>
                
                <div class="user-stats">
                    <div class="stat-item">
                        <span class="stat-number">${user.followers_formatted}</span>
                        <span class="stat-label">Followers</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">${user.following_formatted}</span>
                        <span class="stat-label">Following</span>
                    </div>
                </div>
                
                <div class="user-actions">
                    <button class="btn btn-primary btn-sm expand-btn">
                        <i class="fas fa-eye"></i> View Details
                    </button>
                    <button class="btn btn-secondary btn-sm refresh-btn" data-fid="${user.fid}">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                    <button class="btn btn-error btn-sm delete-btn" data-fid="${user.fid}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                
                <div class="user-details">
                    ${user.bio ? `<div class="user-bio">${user.bio}</div>` : ''}
                    <div class="user-meta">
                        <div class="meta-item">
                            <div class="meta-label">FID</div>
                            <div class="meta-value">${user.fid}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">Custody Address</div>
                            <div class="meta-value">${user.custody_address || 'N/A'}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">Last Updated</div>
                            <div class="meta-value">${user.time_ago}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">Followers</div>
                            <div class="meta-value">${user.followers_count.toLocaleString()}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">Following</div>
                            <div class="meta-value">${user.following_count.toLocaleString()}</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Bind card events
    bindCardEvents() {
        // Expand/collapse details
        document.querySelectorAll('.expand-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const card = btn.closest('.user-card');
                const details = card.querySelector('.user-details');
                const isExpanded = details.classList.contains('expanded');

                if (isExpanded) {
                    details.classList.remove('expanded');
                    btn.innerHTML = '<i class="fas fa-eye"></i> View Details';
                } else {
                    details.classList.add('expanded');
                    btn.innerHTML = '<i class="fas fa-eye-slash"></i> Hide Details';
                }
            });
        });

        // Refresh user
        document.querySelectorAll('.refresh-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const fid = btn.dataset.fid;
                this.refreshUser(fid);
            });
        });

        // Delete user
        document.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const fid = btn.dataset.fid;
                this.deleteUser(fid);
            });
        });
    }

    // Render pagination
    renderPagination(pagination) {
        const container = document.getElementById('pagination-container');
        if (!container) return;

        if (pagination.total_pages <= 1) {
            container.innerHTML = '';
            return;
        }

        let paginationHTML = '<div class="pagination">';

        // Previous button
        if (pagination.has_prev) {
            paginationHTML += `<button class="pagination-btn" onclick="warpSync.loadUsers(${pagination.current_page - 1}, '${this.currentSearch}')">
                <i class="fas fa-chevron-left"></i>
            </button>`;
        } else {
            paginationHTML += `<button class="pagination-btn" disabled>
                <i class="fas fa-chevron-left"></i>
            </button>`;
        }

        // Page numbers
        const startPage = Math.max(1, pagination.current_page - 2);
        const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

        if (startPage > 1) {
            paginationHTML += `<button class="pagination-btn" onclick="warpSync.loadUsers(1, '${this.currentSearch}')">1</button>`;
            if (startPage > 2) {
                paginationHTML += `<span class="pagination-info">...</span>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === pagination.current_page ? 'active' : '';
            paginationHTML += `<button class="pagination-btn ${activeClass}" onclick="warpSync.loadUsers(${i}, '${this.currentSearch}')">${i}</button>`;
        }

        if (endPage < pagination.total_pages) {
            if (endPage < pagination.total_pages - 1) {
                paginationHTML += `<span class="pagination-info">...</span>`;
            }
            paginationHTML += `<button class="pagination-btn" onclick="warpSync.loadUsers(${pagination.total_pages}, '${this.currentSearch}')">${pagination.total_pages}</button>`;
        }

        // Next button
        if (pagination.has_next) {
            paginationHTML += `<button class="pagination-btn" onclick="warpSync.loadUsers(${pagination.current_page + 1}, '${this.currentSearch}')">
                <i class="fas fa-chevron-right"></i>
            </button>`;
        } else {
            paginationHTML += `<button class="pagination-btn" disabled>
                <i class="fas fa-chevron-right"></i>
            </button>`;
        }

        // Page info
        paginationHTML += `<div class="pagination-info">
            Page ${pagination.current_page} of ${pagination.total_pages} (${pagination.total_items} total)
        </div>`;

        paginationHTML += '</div>';
        container.innerHTML = paginationHTML;
    }

    // Loading overlay
    showLoading() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.classList.add('active');
        }
    }

    hideLoading() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.classList.remove('active');
        }
    }

    // Toast notifications
    showToast(message, type = 'info', duration = 5000) {
        const container = document.getElementById('toast-container');
        if (!container) return;

        const toastId = 'toast-' + Date.now();
        const iconMap = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        const titleMap = {
            success: 'Success',
            error: 'Error',
            warning: 'Warning',
            info: 'Info'
        };

        const toastHTML = `
            <div id="${toastId}" class="toast ${type}">
                <div class="toast-header">
                    <div class="toast-title">
                        <i class="${iconMap[type]}"></i>
                        ${titleMap[type]}
                    </div>
                    <button class="toast-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="toast-message">${message}</div>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', toastHTML);

        const toast = document.getElementById(toastId);

        // Show toast
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        // Auto hide
        setTimeout(() => {
            this.closeToast(toast);
        }, duration);
    }

    closeToast(toast) {
        if (toast) {
            toast.classList.remove('show');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }
    }

    // Utility methods
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    timeAgo(dateString) {
        const now = new Date();
        const date = new Date(dateString);
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) return 'just now';
        if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + ' minutes ago';
        if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + ' hours ago';
        if (diffInSeconds < 2592000) return Math.floor(diffInSeconds / 86400) + ' days ago';
        if (diffInSeconds < 31536000) return Math.floor(diffInSeconds / 2592000) + ' months ago';

        return Math.floor(diffInSeconds / 31536000) + ' years ago';
    }
}

// Initialize WarpSync when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.warpSync = new WarpSync();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WarpSync;
}
