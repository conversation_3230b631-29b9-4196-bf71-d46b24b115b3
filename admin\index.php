<?php
/**
 * WarpSync Admin Dashboard
 * Dashboard untuk admin mengelola users dan melihat statistik
 */

define('WARPSYNC_APP', true);
require_once '../config/config.php';
require_once '../includes/functions.php';

// Page variables
$pageTitle = 'Admin Dashboard';
$pageHeader = 'Admin Dashboard';
$pageDescription = 'Manage Farcaster users and view application statistics';
$currentPage = 'admin';
$isAdmin = true;

// Get statistics
try {
    $stats = getAppStats();
} catch (Exception $e) {
    $stats = [
        'total_users' => 0,
        'users_today' => 0,
        'last_update' => null,
        'recent_activity' => []
    ];
}
?>

<?php include '../includes/header.php'; ?>

<div class="container">
    <!-- Statistics Cards -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number"><?php echo number_format($stats['total_users']); ?></div>
                <div class="stat-label">Total Users</div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-user-plus"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number"><?php echo number_format($stats['users_today']); ?></div>
                <div class="stat-label">Added Today</div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number">
                    <?php echo $stats['last_update'] ? timeAgo($stats['last_update']) : 'Never'; ?>
                </div>
                <div class="stat-label">Last Update</div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-sync-alt"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number"><?php echo count($stats['recent_activity']); ?></div>
                <div class="stat-label">Recent Activities</div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="admin-actions">
        <h2>Quick Actions</h2>
        <div class="action-buttons">
            <a href="add_user.php" class="btn btn-primary btn-lg">
                <i class="fas fa-plus"></i> Add New User
            </a>
            <a href="manage.php" class="btn btn-secondary btn-lg">
                <i class="fas fa-cogs"></i> Manage Users
            </a>
            <button id="refresh-all-admin" class="btn btn-warning btn-lg">
                <i class="fas fa-sync-alt"></i> Refresh All Data
            </button>
            <a href="../" class="btn btn-success btn-lg">
                <i class="fas fa-eye"></i> View Public Site
            </a>
        </div>
    </div>
    
    <!-- Recent Activity -->
    <div class="recent-activity">
        <h2>Recent Activity</h2>
        <div class="activity-list">
            <?php if (empty($stats['recent_activity'])): ?>
                <div class="empty-state">
                    <i class="fas fa-history"></i>
                    <h3>No recent activity</h3>
                    <p>Activity will appear here when users are added or updated.</p>
                </div>
            <?php else: ?>
                <?php foreach ($stats['recent_activity'] as $activity): ?>
                    <div class="activity-item">
                        <div class="activity-icon">
                            <?php
                            $iconMap = [
                                'ADD_USER' => 'fas fa-user-plus',
                                'REFRESH_USER' => 'fas fa-sync-alt',
                                'DELETE_USER' => 'fas fa-user-minus'
                            ];
                            $icon = $iconMap[$activity['action_type']] ?? 'fas fa-info-circle';
                            ?>
                            <i class="<?php echo $icon; ?>"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">
                                <?php
                                $actionMap = [
                                    'ADD_USER' => 'User Added',
                                    'REFRESH_USER' => 'User Refreshed',
                                    'DELETE_USER' => 'User Deleted'
                                ];
                                echo $actionMap[$activity['action_type']] ?? 'Unknown Action';
                                ?>
                            </div>
                            <div class="activity-details">
                                <?php if ($activity['fid']): ?>
                                    FID: <?php echo htmlspecialchars($activity['fid']); ?>
                                <?php endif; ?>
                                <?php if ($activity['details']): ?>
                                    - <?php echo htmlspecialchars($activity['details']); ?>
                                <?php endif; ?>
                            </div>
                            <div class="activity-time">
                                <?php echo timeAgo($activity['created_at']); ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// Admin-specific JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Refresh all users button
    const refreshAllBtn = document.getElementById('refresh-all-admin');
    if (refreshAllBtn) {
        refreshAllBtn.addEventListener('click', async function() {
            if (!confirm('This will refresh all users data from Farcaster API. This may take a while. Continue?')) {
                return;
            }
            
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
            
            try {
                const response = await fetch('../api/refresh_user.php?all=1', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert('All users refreshed successfully!');
                    location.reload();
                } else {
                    alert('Some users failed to refresh: ' + data.message);
                }
            } catch (error) {
                alert('Error refreshing users: ' + error.message);
            } finally {
                this.disabled = false;
                this.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh All Data';
            }
        });
    }
});
</script>

<?php include '../includes/footer.php'; ?>
