<?php
/**
 * WarpSync Database Connection Test
 * File untuk testing koneksi database dan setup
 */

define('WARPSYNC_APP', true);
require_once 'config/config.php';

// Test database connection
echo "<h1>WarpSync Database Connection Test</h1>";

try {
    $database = new Database();
    $result = $database->testConnection();
    
    if ($result['status'] === 'success') {
        echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
        echo "<strong>✓ Database Connection: SUCCESS</strong><br>";
        echo $result['message'];
        echo "</div>";
        
        // Test if tables exist
        echo "<h2>Table Structure Test</h2>";
        
        $conn = $database->getConnection();
        
        // Check farcaster_users table
        try {
            $stmt = $conn->query("DESCRIBE farcaster_users");
            echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
            echo "<strong>✓ farcaster_users table: EXISTS</strong>";
            echo "</div>";
            
            echo "<h3>farcaster_users table structure:</h3>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
            
            while ($row = $stmt->fetch()) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Default'] ?? 'NULL') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
        } catch (Exception $e) {
            echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
            echo "<strong>✗ farcaster_users table: NOT FOUND</strong><br>";
            echo "Error: " . $e->getMessage();
            echo "<br><br><strong>Please run the SQL script:</strong><br>";
            echo "<code>mysql -u root -p warpsync < sql/warpsync.sql</code>";
            echo "</div>";
        }
        
        // Check activity_logs table
        try {
            $stmt = $conn->query("DESCRIBE activity_logs");
            echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
            echo "<strong>✓ activity_logs table: EXISTS</strong>";
            echo "</div>";
        } catch (Exception $e) {
            echo "<div style='color: orange; padding: 10px; border: 1px solid orange; margin: 10px 0;'>";
            echo "<strong>⚠ activity_logs table: NOT FOUND</strong><br>";
            echo "This table is optional but recommended for logging.";
            echo "</div>";
        }
        
        // Check app_settings table
        try {
            $stmt = $conn->query("SELECT * FROM app_settings");
            echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
            echo "<strong>✓ app_settings table: EXISTS</strong>";
            echo "</div>";
            
            echo "<h3>Current app settings:</h3>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>Setting Key</th><th>Value</th><th>Description</th></tr>";
            
            while ($row = $stmt->fetch()) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($row['setting_key']) . "</td>";
                echo "<td>" . htmlspecialchars($row['setting_value']) . "</td>";
                echo "<td>" . htmlspecialchars($row['description']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
        } catch (Exception $e) {
            echo "<div style='color: orange; padding: 10px; border: 1px solid orange; margin: 10px 0;'>";
            echo "<strong>⚠ app_settings table: NOT FOUND</strong><br>";
            echo "This table is optional but recommended for configuration.";
            echo "</div>";
        }
        
        // Test sample data insertion
        echo "<h2>Sample Data Test</h2>";
        try {
            // Check if any users exist
            $stmt = $conn->query("SELECT COUNT(*) as count FROM farcaster_users");
            $result = $stmt->fetch();
            $userCount = $result['count'];
            
            echo "<div style='color: blue; padding: 10px; border: 1px solid blue; margin: 10px 0;'>";
            echo "<strong>Current users in database: " . $userCount . "</strong>";
            echo "</div>";
            
            if ($userCount == 0) {
                echo "<div style='color: orange; padding: 10px; border: 1px solid orange; margin: 10px 0;'>";
                echo "<strong>No users found.</strong><br>";
                echo "You can add users through the admin panel: <a href='admin/add_user.php'>Add User</a>";
                echo "</div>";
            }
            
        } catch (Exception $e) {
            echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
            echo "<strong>✗ Error checking user data:</strong><br>";
            echo $e->getMessage();
            echo "</div>";
        }
        
    } else {
        echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
        echo "<strong>✗ Database Connection: FAILED</strong><br>";
        echo $result['message'];
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
    echo "<strong>✗ Connection Test Failed:</strong><br>";
    echo $e->getMessage();
    echo "</div>";
}

// Test API endpoints
echo "<h2>API Endpoints Test</h2>";

$apiEndpoints = [
    'search_users.php' => 'Search Users API',
    'fetch_user.php' => 'Fetch User API',
    'refresh_user.php' => 'Refresh User API'
];

foreach ($apiEndpoints as $endpoint => $name) {
    $url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/api/' . $endpoint;
    
    echo "<div style='margin: 10px 0;'>";
    echo "<strong>$name:</strong> ";
    echo "<a href='$url' target='_blank'>$url</a>";
    echo "</div>";
}

// Configuration check
echo "<h2>Configuration Check</h2>";

$configChecks = [
    'APP_NAME' => APP_NAME,
    'APP_VERSION' => APP_VERSION,
    'FARCASTER_API_BASE' => FARCASTER_API_BASE,
    'ITEMS_PER_PAGE' => ITEMS_PER_PAGE,
    'API_RATE_LIMIT' => API_RATE_LIMIT
];

echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
echo "<tr><th>Configuration</th><th>Value</th></tr>";

foreach ($configChecks as $key => $value) {
    echo "<tr>";
    echo "<td>" . htmlspecialchars($key) . "</td>";
    echo "<td>" . htmlspecialchars($value) . "</td>";
    echo "</tr>";
}
echo "</table>";

// File permissions check
echo "<h2>File Permissions Check</h2>";

$directories = [
    'config/' => 'Configuration directory',
    'api/' => 'API directory',
    'assets/' => 'Assets directory',
    'admin/' => 'Admin directory'
];

foreach ($directories as $dir => $description) {
    if (is_dir($dir)) {
        if (is_readable($dir)) {
            echo "<div style='color: green; margin: 5px 0;'>";
            echo "✓ $description ($dir): Readable";
            echo "</div>";
        } else {
            echo "<div style='color: red; margin: 5px 0;'>";
            echo "✗ $description ($dir): Not readable";
            echo "</div>";
        }
    } else {
        echo "<div style='color: red; margin: 5px 0;'>";
        echo "✗ $description ($dir): Directory not found";
        echo "</div>";
    }
}

echo "<h2>Next Steps</h2>";
echo "<div style='background: #f0f0f0; padding: 15px; margin: 10px 0;'>";
echo "<ol>";
echo "<li>If database connection failed, check your database credentials in <code>config/database.php</code></li>";
echo "<li>If tables don't exist, import the SQL file: <code>mysql -u root -p warpsync < sql/warpsync.sql</code></li>";
echo "<li>Visit the main page: <a href='index.php'>index.php</a></li>";
echo "<li>Visit the admin panel: <a href='admin/'>admin/</a></li>";
echo "<li>Add your first user: <a href='admin/add_user.php'>admin/add_user.php</a></li>";
echo "</ol>";
echo "</div>";

echo "<div style='margin-top: 20px; padding: 10px; background: #e6f3ff; border: 1px solid #0066cc;'>";
echo "<strong>WarpSync Setup Complete!</strong><br>";
echo "If all tests pass, your WarpSync installation is ready to use.";
echo "</div>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background: #f5f5f5;
}

h1, h2, h3 {
    color: #333;
}

table {
    width: 100%;
    background: white;
}

th, td {
    padding: 8px;
    text-align: left;
}

th {
    background: #f0f0f0;
}

code {
    background: #f0f0f0;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

a {
    color: #0066cc;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
